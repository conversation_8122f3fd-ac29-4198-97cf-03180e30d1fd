package com.les.its.open.front.websocket.service;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.bean.MonitorParam;
import com.les.its.open.front.websocket.bean.ResponseCode;
import com.les.its.open.front.websocket.bean.ResponseMessage;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @ClassName: MessageService
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/21 9:53
 */
@Service
@Slf4j
public class WsMessageService {

    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    public static final String BROADCAST_DESTINATION = "/topic/message";

    @Getter
    private MonitorParam monitorParam;

    private long hbTime;

    @PostConstruct
    public void initMonitorParam(){
        monitorParam = new MonitorParam("9999999", 0);
        hbTime = System.currentTimeMillis();
    }

    /**
     * 更新监控参数
     * @param controllerId
     * @param flag
     */
    public void updateMonitorParam(String controllerId, int flag){
        if(monitorParam != null){
            monitorParam.setControllerId(controllerId);
            monitorParam.setFlag(flag);
        }
    }

    /**
     * 更新心跳时间
     */
    public void updateHbTime(){
        hbTime = System.currentTimeMillis();
    }


    /**
     * 当前是否处于监控状态
     * @param controllerId
     * @return
     */
    public  boolean  isMonitor(String controllerId){
        return monitorParam != null
                && monitorParam.getFlag() == 1
                && monitorParam.getControllerId().equals(controllerId)
                && (System.currentTimeMillis() - hbTime < 30000);
    }


    /**
     * 广播数据项变更
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void sendMsg(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_SND.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION,message);
    }

    /**
     * 广播数据项变更
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void recvMsg(String controllerId, Object data)
    {

        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_RCV.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }



    /**
     * 广播数据项变更
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void sendMsgNats(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_SND_NATS.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION,message);
    }

    /**
     * 广播数据项变更
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void recvMsgNats(String controllerId, Object data)
    {

        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_RCV_NATS.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }


    /**
     * 收到相位状态数据
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void recvPhaseStatus(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_PHASE_STATUS.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }

    /**
     * 收到阶段状态数据
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void recvStageStatus(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_STAGE_STATUS.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }

    /**
     * 收到链路质量数据
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void revRTT(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_RTT.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }

    /**
     *
     * @param controllerId
     * @param data
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void revLinkStatus(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_LINK_STATUS.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }

    /**
     * 收到灯故障数据
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    public void revFault(String controllerId, Object data)
    {
        if(!isMonitor(controllerId)){
            return;
        }

        ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_TAB_FAULT.code())
                .data(JSONObject.toJSONString(data)).build();

        simpMessagingTemplate.convertAndSend(BROADCAST_DESTINATION, message);
    }

}
