package com.les.its.open.bussiness.process;

import com.les.its.open.bussiness.bean.LinkLogEntity;
import com.les.its.open.bussiness.repository.LinkLogEntityRepository;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.controller.dto.TimeQuery;
import com.les.its.open.utils.UtcTimeUtils;
import com.myweb.commons.dao.RepositoryDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class LinkLogProcess {

    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private LinkLogEntityRepository repository;

    @Value("${app.link.data-retention.days:30}")
    private int retentionDays;

    // 每天凌晨4点执行
    @Scheduled(cron = "${app.link.cleanup.cron:0 0 4 * * ?}")
    @Async(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    public void cleanupExpiredDataLink() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

        try {
            long deletedCount = repository.deleteByCreatedTimeBefore(UtcTimeUtils.convert2UtcTimestamp(cutoffTime));
            log.error("清理link过期数据完成，删除记录数: {}, 截止时间: {}", deletedCount, cutoffTime);
        } catch (Exception e) {
            log.error("数据link清理失败", e);
        }
    }

    @EventListener
    @Async(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    @Transactional
    public void saveLinkLog(LinkLogEntity linkLogEntity) {
        log.trace("保存link数据项-{}", linkLogEntity);
        repositoryDao.save(linkLogEntity, LinkLogEntity.class);
    }


    /**
     * 根据信号机编号以及时间范围查询
     * @param controllerId
     * @param timeQuery
     * @return
     */
    public List<LinkLogEntity> queryLinkLog(String controllerId, TimeQuery timeQuery) {
        long startTime = UtcTimeUtils.convert2UtcTimestamp(timeQuery.getStartTime());
        long endTime = UtcTimeUtils.convert2UtcTimestamp(timeQuery.getEndTime());
        return repository.findByControllerIdAndTimeStampLogBetween(controllerId, startTime, endTime);
    }


}
