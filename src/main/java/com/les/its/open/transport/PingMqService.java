package com.les.its.open.transport;


import com.les.its.open.protocol.InterProtocolType;
import com.les.its.open.protocol.common.InterProtocol;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @ClassName: PingMqService
 * @Description:
 * @Author: king
 * @CreateDate: 2020/3/24 11:35
 */
@Component
@Slf4j
public class PingMqService {

    @Autowired
    private MessageSender messageSender;

    @Scheduled(initialDelay = 5000, fixedRate = 5000)
    private void pingMq() {
        log.trace("send ping message to MQ");
        InterProtocol interProtocol = new InterProtocol();
        interProtocol.setInterProtocolType(InterProtocolType.UNKOWN_MESSAGE);
        messageSender.sendDirect(interProtocol);
    }
}
