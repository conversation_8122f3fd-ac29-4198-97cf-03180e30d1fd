package com.les.its.open.area.net.msg.status.sub;

public enum FaultStatus {
    /**
     * 生成
     */
    GEN(0x01, "生成"),

    /**
     * 消失
     */
    GONE(0x00, "消失"),

    UNKNOWN(0xFFFF, "未知故障");

    private final int code;
    private final String description;

    FaultStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据故障代码获取故障类型
     * @param code 故障代码
     * @return 故障类型枚举，如果未找到返回null
     */
    public static FaultStatus getByCode(int code) {
        for (FaultStatus faultStatus : values()) {
            if (faultStatus.getCode() == code) {
                return faultStatus;
            }
        }
        return UNKNOWN;
    }


    @Override
    public String toString() {
        return String.format("FaultStatus{code=0x%02X, description='%s'}", code, description);
    }

}
