package com.les.its.open.area.message.api;

import com.les.its.open.area.message.MqMessageProcess;
import com.les.its.open.area.message.MqMsgSimSyncProcess;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RequestMapping("/openles/lookAllNats")
@Slf4j
@RestController
public class AllLookNatslController {


    @Autowired
    private MqMsgSimSyncProcess mqMsgSimSyncProcess;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private MqMessageProcess mqMessageProcess;

    /**
     * 同步一键调看信号机参数
     * @param controllerId
     * @return
     */
    @PostMapping("/{controllerId}")
    public JsonResult<?> lookSignalController(@PathVariable String controllerId, @RequestBody List<String> exportTypes) {

        StopWatch stopWatch = new StopWatch("开始Nats调看信号机" + controllerId + "参数" + exportTypes);

        //信号机参数
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            log.error("设备{}不存在", controllerId);
            return new JsonResult<>(false, String.valueOf(ResultCode.FAILED_NO_CONTROLLER.code()),
                    ResultCode.FAILED_NO_CONTROLLER.message(), null);
        }

        List<Map<String, List<Object>>> allParams = new ArrayList<>();
        exportTypes.forEach(
            objectId -> {
                Map<String, List<Object>> eachParam = new HashMap<>();
                allParams.add(eachParam);

                Optional<LookLoadMqObject> lookLoadMqObjectOp = LookLoadMqObject.getType(objectId);

                stopWatch.start("调看-" + objectId + "-"
                        + (lookLoadMqObjectOp.isPresent() ? lookLoadMqObjectOp.get().des() : "未知"));
                Optional<Object> natsParam = mqMsgSimSyncProcess.getNatsParam(controllerId, objectId);
                stopWatch.stop();
                if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){

                    Optional<MqMsgBaseHandler> mqMsgBaseHandlerOp = mqMessageProcess.getMqMsgBaseHandlerMap().values().stream().filter(
                            mqMsgBaseHandler -> mqMsgBaseHandler.getObjectId().equalsIgnoreCase(objectId)
                    ).findAny();



                    eachParam.put(objectId +
                            (mqMsgBaseHandlerOp
                                    .map(mqMsgBaseHandler -> "-" + mqMsgBaseHandler.dataType().getSimpleName()
                                            + (lookLoadMqObjectOp.isPresent() ? ("-") + lookLoadMqObjectOp.get().des() : ""))
                                    .orElse("")), mqMessage.getObjectList());
                }
            }
        );

        log.error("$$$$Nats{}参数调看{}",  controllerId, stopWatch.prettyPrint());

        return new JsonResult<>(true, "20000", "获取信号参数正常", allParams);

    }

}
