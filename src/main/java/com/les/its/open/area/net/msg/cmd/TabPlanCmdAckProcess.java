package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabPlanCmd;
import com.les.its.open.area.juncer.msg.cmd.TabPlanCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabPlanCmdAckProcess extends TabOuterBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_PLAN_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return (13 +  16 *3);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabPlanCmdAck tabPlanCmdAck = new TabPlanCmdAck();
            tabPlanCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabPlanCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabPlanCmd tabPlanCmd = new TabPlanCmd();
            tabPlanCmdAck.setTabPlanCmd(tabPlanCmd);
            tabPlanCmd.setCmdNo(buf.readUnsignedByte() & 0xff);
            tabPlanCmd.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            tabPlanCmd.setCharacter(buf.readUnsignedByte() & 0xff);
            tabPlanCmd.setCmdType(buf.readUnsignedByte() & 0xff);
            tabPlanCmd.setDuration(buf.readUnsignedShortLE());
            tabPlanCmd.setStageSeq(buf.readUnsignedByte() & 0xff);
            tabPlanCmd.setOffset(buf.readUnsignedShortLE());
            tabPlanCmd.setStageNum(buf.readUnsignedByte() & 0xff);
            List<Integer> stageNos = new ArrayList<>();
            tabPlanCmd.setStageNos(stageNos);
            for (int i = 0; i < 16; i++) {
                tabPlanCmd.getStageNos().add(buf.readUnsignedByte() & 0xff);
            }
            List<Integer> stageTimes = new ArrayList<>();
            tabPlanCmd.setStageTimes(stageTimes);
            for (int i = 0; i < 16; i++) {
                tabPlanCmd.getStageTimes().add(buf.readUnsignedShortLE());
            }
            tabPlanCmd.setPhaseTableNo(buf.readUnsignedByte() & 0xff);

            buf.release();

            return Optional.of(tabPlanCmdAck);
        }

        return Optional.empty();
    }

}
