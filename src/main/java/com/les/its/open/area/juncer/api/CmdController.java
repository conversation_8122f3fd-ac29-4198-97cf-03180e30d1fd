package com.les.its.open.area.juncer.api;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.cmd.TabCmdAck;
import com.les.its.open.area.juncer.msg.cmd.TabSetStage;
import com.les.its.open.area.juncer.msg.param.lookload.dto.ReportSetting;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabCmdService;
import com.les.its.open.area.net.proc.TabLoadService;
import com.les.its.open.area.net.proc.TabLookService;
import com.les.its.open.front.controller.NatsCmdController;
import com.les.its.open.utils.ResultCode;
import com.les.its.open.utils.ResultVo;
import com.les.its.open.utils.ResultVoUtil;
import com.myweb.commons.persistence.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequestMapping("/openles/cmd")
@Slf4j
@RestController
public class CmdController {

    @Autowired
    private TabCmdService tabCmdService;

    @Autowired
    private TabLookService tabLookService;

    @Autowired
    private TabLoadService tabLoadService;

    @Autowired
    private NatsCmdController natsCmdController;

    /**
     * 指定相位控制命令
     * @param crossingId
     * @param data
     * @return
     */
    @GetMapping("/{crossingId}")
    public ResultVo<?> setStage(@PathVariable String crossingId,
                                            @RequestBody String data) {

        TabSetStage tabSetStage = JSONObject.parseObject(data, TabSetStage.class);
        if(tabSetStage == null) {
            return ResultVoUtil.result(ResultCode.FAILED_SET_STAGE_PARAM);
        }

        Optional<Object> loadOp = tabCmdService.setStage(crossingId, tabSetStage);
        if(loadOp.isPresent()){
            return ResultVoUtil.success(loadOp.get());
        }else{
            return ResultVoUtil.result(ResultCode.FAILED_CMD);
        }
    }

    /**
     * 信号机校时
     * @param controllerId
     * @return
     */
    @PostMapping("/syncTime/{controllerId}")
    public JsonResult<?> syncTime(@PathVariable String controllerId) {
        log.error("收到信号机校时命令{}", controllerId);
        Optional<Object> loadOp = tabCmdService.syncTime(controllerId);
        if(loadOp.isPresent()){
            TabCmdAck ack = (TabCmdAck) (loadOp.get());
            if(ack.getAck() == 0) {
                return new JsonResult<>(true, "20000", "信号机校时正常", loadOp.get());
            }else {
                return new JsonResult<>(false, "50000", "信号机校时异常", loadOp.get());
            }
        }else{
            return JsonResult.error(ResultCode.FAILED_LOAD.message());
        }
    }

    /**
     * 获取上传控制接口
     * @param controllerId
     * @return
     */
    @GetMapping("/report/{controllerId}")
    public JsonResult<?> report(@PathVariable String controllerId) {

        Optional<Object> looked = tabLookService.look(controllerId, ParamMsgType.PARAM_REPORT_SETTING, 1, 1);
        if(looked.isPresent()){
            if(looked.get() instanceof List){
                return new JsonResult<>(true, "20000", "调看信号参数返回",  ((List<?>) looked.get()).get(0) );
            }else{
                return JsonResult.error(ResultCode.FAILED_LOOK.message());
            }
        }else{
            return JsonResult.error(ResultCode.FAILED_LOOK.message());
        }
    }



    /**
     * 设置上传控制接口
     * @param controllerId
     * @return
     */
    @PostMapping("/report/{controllerId}")
    public JsonResult<?> report(@PathVariable String controllerId, @RequestBody @Valid ReportSetting reportSetting) {


        JsonResult<?> result = natsCmdController.startTrans(controllerId);
        if(!result.isSuccess()){
            return result;
        }

        List<ReportSetting> reportSettings = new ArrayList<>();
        reportSettings.add(reportSetting);

        Optional<Object> loadOp = tabLoadService.load(controllerId, ParamMsgType.PARAM_REPORT_SETTING, JSONObject.toJSONString(reportSettings));
        if(loadOp.isPresent()){

            JsonResult<?> verify = natsCmdController.verify(controllerId);
            if(!verify.isSuccess()){
                return result;
            }

            return new JsonResult<>(true, "20000", "设置信号参数正常", loadOp.get());
        }else{
            return JsonResult.error(ResultCode.FAILED_LOAD.message());
        }

    }


}
