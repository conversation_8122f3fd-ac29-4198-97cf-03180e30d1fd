package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.PhaseParamInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 相位参数
 */
@Data
public class PhaseParam implements SgpTransAble {
    public static final String MqObjectId = "10005";

    /**
     * 相位参数列表
     */
    @NotNull(message = "{basic.param.phase.phaseParams.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.phase.phaseParams.size}")
    @Valid
    private List<PhaseParamInfo> phaseParams;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 