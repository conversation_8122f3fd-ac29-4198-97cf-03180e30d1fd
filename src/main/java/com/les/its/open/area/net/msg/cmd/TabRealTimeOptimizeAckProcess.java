package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabRealTimeOptimize;
import com.les.its.open.area.juncer.msg.cmd.TabRealTimeOptimizeAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabRealTimeOptimizeAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_REAL_TIME_OPTIMIZE_ACK;
    }

    @Override
    public int oneItemSize() {
        return (6);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabRealTimeOptimizeAck tabRealTimeOptimizeAck = new TabRealTimeOptimizeAck();
            tabRealTimeOptimizeAck.setAck(buf.readUnsignedByte() & 0xff);
            tabRealTimeOptimizeAck.setReason(buf.readUnsignedByte() & 0xff);

            TabRealTimeOptimize tabRealTimeOptimize = new TabRealTimeOptimize();
            tabRealTimeOptimizeAck.setTabRealTimeOptimize(tabRealTimeOptimize);
            tabRealTimeOptimize.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            tabRealTimeOptimize.setStageNo(buf.readUnsignedByte() & 0xff);
            tabRealTimeOptimize.setStageTime(buf.readUnsignedShortLE());

            buf.release();

            return Optional.of(tabRealTimeOptimizeAck);
        }

        return Optional.empty();
    }

}
