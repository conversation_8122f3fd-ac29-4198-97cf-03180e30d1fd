package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabCenterStep;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabCenterStepProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_CENTER_STEP;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabCenterStep.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabCenterStep tabCenterStep) {
            msgCode.set(msgType().getCode());

            byte[] body = new byte[5];
            body[0] = (byte) (tabCenterStep.getCrossingSeqNo() & 0xff);
            body[1] = (byte) (tabCenterStep.getCharacter() & 0xff);
            body[2] = (byte) (tabCenterStep.getStepMode() & 0xff);
            body[3] = (byte) (tabCenterStep.getDuration() & 0xff) ;
            body[4] = (byte) ((tabCenterStep.getDuration() >> 8) & 0xff) ;

            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}
