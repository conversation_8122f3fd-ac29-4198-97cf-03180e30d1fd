package com.les.its.open.area.net.msg;

import lombok.Getter;

@Getter
public enum ParamMsgType {

    /*加载调看参数*/
    PARAM_DEVICE_INFO(0x0100, 1, "设备信息"),
    PARAM_BOARD_INFO(0x0101, 1, "板卡信息"),
    PARAM_PRE_CONFIG(0x0103, 1, "生产配置"),
    PARAM_BASE_INFO(0x0200, 1, "基础信息"),
    PARAM_CONTROLLER_TIME(0x0201, 1, "信号机时间"),
    PARAM_NETWORK_CONFIGURATION(0x0202, 1, "信号机网络配置"),
    PARAM_HOST_NETWORK(0x0203, 1, "上位机网络配置"),
    PARAM_BASIC_PARAM(0x0204, 1, "参数信息"),
    PARAM_MODE_PRIORITY(0x0205, 1, "控制模式优先级"),
    PARAM_BASIC_CONFIG(0x0206, 1, "基础配置"),
    PARAM_CONSTRAINT(0x0207, 1, "相位约束信息"),
    PARAM_BASIC_PARAM_USED(0x0208, 1, "使用参数信息"),
    PARAM_LANE_CHANNELIZATION_INFO(0x0300, 8, "路口渠化信息"),
    PARAM_DETECTOR(0x0400, 64, "检测器信息"),
    PARAM_COUNT_DOWN_INFO(0x0500, 1, "倒计时基础配置信息"),
    PARAM_COUNT_DOWN_DISPLAY_PARAM(0x0501, 32, "倒计时屏参数"),
    PARAM_LAMP_FAULT_DETECT(0x0600, 1, "灯检测开关"),
    PARAM_LAMP_FAULT_DETECT_FLAG(0x0601, 1, "灯故障检测标记"),
    PARAM_UI_THRESHOLD(0x0602, 1, "灯检测电压电流阈值配置"),
    PARAM_LAMP_FAULT_THRESHOLD(0x0603, 64, "灯检测门限配置"),
    PARAM_STAGE_TRANS_CONSTRAINT(0x0700, 4, "过渡约束"),
    PARAM_MODE_TRANS_CONSTRAINT(0x0701, 1, "运行模式过渡约束"),
    PARAM_LIGHTS_GROUP(0x0800, 64, "灯组信息"),
    PARAM_PHASE(0x0901, 64, "相位灯"),
    PARAM_INCOMPATIBLE_PHASE(0x0902, 1, "相位冲突对立表"),
    PARAM_INTER_GREEN_TIME(0x0903, 4, "相位绿间隔配置表"),
    PARAM_MODE_INTER_GREEN(0x0904, 1, "运行模式绿间隔"),
    PARAM_PHASE_TABLE_TRANS(0x0905, 10, "相位表-过渡"),
    PARAM_PHASE_TABLE_LIMIT_GREEN(0x0906, 1, "相位表-限制绿"),
    PARAM_ACTUATED_PHASE(0x0907, 64, "感应相位信息"),
    PARAM_PHASE_DEMAND_EXTENSION(0x0908, 64, "相位需求与延长"),
    PARAM_LAMP_SEQUENCE(0x0909, 64, "相位灯序"),
    PARAM_STAGE(0x0A00, 64, "阶段"),
    PARAM_STARTUP_STAGE(0x0A01, 1, "相位阶段启动信息"),
    PARAM_PLAN(0x0C00, 128, "方案",  64),
    PARAM_DAY_PLAN(0x0D00, 128, "日计划", 64),
    PARAM_SCHEDULE(0x0E00, 128, "调度计划"),
    PARAM_EMERGENCY(0x0F00, 64, "紧急"),
    PARAM_PRIORITY(0x1000, 64, "优先"),
    PARAM_LOGIC_OUTPUT(0x1100, 64, "逻辑输出"),
    PARAM_TIMER(0x1101, 64, "定时器"),
    PARAM_USER_FLAG(0x1102, 64, "用户变量"),
    PARAM_CONDITIONING(0x1103, 64, "条件计算"),
    PARAM_ACTION(0x1104, 64, "动作"),
    PARAM_REPORT_SETTING(0x1200, 1, "上报设置"),
    PARAM_OFFLINE_SETTING(0x1300, 1, "离线设置"),
    PARAM_MANUAL_PANEL(0x1400, 8, "手动控制"),
    PARAM_CALL_CANCEL(0x1500, 64, "调用删除"),

    PARAM_UNKNOWN(0xFFFF, 0, "未知"),
    ;

    private final int code;
    private final int max;
    //由于报文大小限制，标记一次允许的最大个数
    private final int lookMax;
    private final String description;

    ParamMsgType(int code, int max, String description) {
        this.code = code;
        this.max = max;
        this.description = description;
        this.lookMax = max;
    }

    ParamMsgType(int code, int max, String description, int lookMax) {
        this.code = code;
        this.max = max;
        this.description = description;
        this.lookMax = lookMax;
    }
    
    @Override
    public String toString() {
        return String.format("%s(0x%04X) - 最大%d - %s", name(), code, max, description);
    }

    /**
     * 根据 code 获取对应的枚举类型
     * @param code 枚举的 code 值
     * @return 匹配的枚举类型，如果没有匹配则返回 PARAM_UNKNOWN
     */
    public static ParamMsgType fromCode(int code) {
        for (ParamMsgType type : ParamMsgType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PARAM_UNKNOWN;
    }
}