package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.*;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 控制器参数信息
 */
@Data
public class ControllerParam implements SgpTransAble {
    public static final String MqObjectId = "10002";

    /**
     * 设备版本
     */
    @NotNull(message = "{basic.param.controller.deviceVersion.notNull}")
    private String deviceVersion;

    /**
     * 主控板序列号
     */
    @NotNull(message = "{basic.param.controller.controlBoardSerialNumber.notNull}")
    private String controlBoardSerialNumber;

    /**
     * 灯驱板数
     * [1-16]
     */
    @NotNull(message = "{basic.param.controller.lampDriverBoardMaxNumber.notNull}")
    @Min(value = 1, message = "{basic.param.controller.lampDriverBoardMaxNumber.min}")
    @Max(value = 16, message = "{basic.param.controller.lampDriverBoardMaxNumber.max}")
    private Integer lampDriverBoardMaxNumber;

    /**
     * 灯驱板信息
     */
    @NotNull(message = "{basic.param.controller.lampDriverBoards.notNull}")
    //@Size(min = 16, max = 16, message = "{basic.param.controller.lampDriverBoards.size}")
    //@Valid
    private List<LampDriverBoard> lampDriverBoards;

    // 灯控板灯组数
    @DigitsInSet(acceptedValues = {4, 6, 8}, message = "{basic.param.controller.lampGroupNumber.digitsInSet}")
    private int lampGroupNumber;

    /**
     * 检测板数
     * [0-4]
     */
    @NotNull(message = "{basic.param.controller.detectionBoardMaxNumber.notNull}")
    @Min(value = 0, message = "{basic.param.controller.detectionBoardMaxNumber.min}")
    @Max(value = 4, message = "{basic.param.controller.detectionBoardMaxNumber.max}")
    private Integer detectionBoardMaxNumber;

    /**
     * 检测板信息
     */
    @NotNull(message = "{basic.param.controller.detectionBoards.notNull}")
    //@Size(min = 4, max = 4, message = "{basic.param.controller.detectionBoards.size}")
    //@Valid
    private List<DetectionBoard> detectionBoards;

    /**
     * 信息板数
     */
    @NotNull(message = "{basic.param.controller.informationBoardMaxNumber.notNull}")
    @Min(value = 0, message = "{basic.param.controller.informationBoardMaxNumber.min}")
    @Max(value = 1, message = "{basic.param.controller.informationBoardMaxNumber.max}")
    private Integer informationBoardMaxNumber;

    /**
     * 信息板信息
     */
    @NotNull(message = "{basic.param.controller.informationBoards.notNull}")
    @Size(min = 1, max = 1, message = "{basic.param.controller.informationBoards.size}")
    //@Valid
    private List<InformationBoard> informationBoards;

    // 系统协调零点
    @NotNull(message = "{basic.param.controller.systemCordRef.notNull}")
    @Range(min = 0, max = 1439, message = "{basic.param.controller.systemCordRef.range}")
    private Integer systemCordRef;

    // 看门狗最大绿
    @NotNull(message = "{basic.param.controller.maxGreenWatchDog.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.controller.maxGreenWatchDog.range}")
    private Integer maxGreenWatchDog;

    // 启动绿灯间隔
    @NotNull(message = "{basic.param.controller.startInterGreen.notNull}")
    @Range(min = 0, max = 32, message = "{basic.param.controller.startInterGreen.range}")
    private Integer startInterGreen;

    // 可变标记个数
    @NotNull(message = "{basic.param.controller.switchedSigns.notNull}")
    @Range(min = 0, max = 32, message = "{basic.param.controller.switchedSigns.range}")
    private Integer switchedSigns;

    // 阶段跳转禁止
    @NotNull(message = "{basic.param.controller.stageSkipProhibited.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.controller.stageSkipProhibited.range}")
    private Integer stageSkipProhibited;

    // 实际相位数
    @NotNull(message = "{basic.param.controller.usedPhaseNum.notNull}")
    @Range(min = 1, max = 64, message = "{basic.param.controller.usedPhaseNum.range}")
    private Integer usedPhaseNum;


    // 实际阶段数
    @NotNull(message = "{basic.param.controller.usedStageNum.notNull}")
    @Range(min = 2, max = 64, message = "{basic.param.controller.usedStageNum.range}")
    private Integer usedStageNum;

    // 降级动作
    @NotNull(message = "{basic.param.controller.degradeAction.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.controller.degradeAction.range}")
    private Integer degradeAction;

    // 紧急优先执行绿看门狗
    @NotNull(message = "{basic.param.controller.hurryCallExecuteWatchDog.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.controller.hurryCallExecuteWatchDog.range}")
    private Integer hurryCallExecuteWatchDog;

    // 黄灯行人灯态标志
    @NotNull(message = "{basic.param.controller.yellowAsWaitIndicator.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.controller.yellowAsWaitIndicator.range}")
    private Integer yellowAsWaitIndicator;

    @NotNull(message = "{basic.param.controller.maxManualControlTime.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.controller.maxManualControlTime.range}")
    private Integer maxManualControlTime;

    @NotNull(message = "{basic.param.controller.flashOnTime.notNull}")
    @Range(min = 400, max = 1000, message = "{basic.param.controller.flashOnTime.range}")
    private Integer flashOnTime;

    @NotNull(message = "{basic.param.controller.flashOffTime.notNull}")
    @Range(min = 400, max = 1000, message = "{basic.param.controller.flashOffTime.range}")
    private Integer flashOffTime;

    /**
     * 故障响应
     */
    @NotNull(message = "{basic.param.controller.lampFaultDetect.notNull}")
    @Valid
    private LampFaultDetect lampFaultDetect;

    /**
     * 约束信息
     */
    @NotNull(message = "{basic.param.controller.constraint.notNull}")
    @Valid
    private Constraint constraint;

    /**
     * 模式优先级
     * 数组固定长度255，下标表示控制方式编号
     */
    @NotNull(message = "{basic.param.controller.modePriority.notNull}")
    @Size(min = 255, max = 255, message = "{basic.param.controller.modePriority.size}")
    private List<Integer> modePriority;


    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 