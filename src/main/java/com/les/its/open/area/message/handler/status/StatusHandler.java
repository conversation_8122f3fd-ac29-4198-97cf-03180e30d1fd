package com.les.its.open.area.message.handler.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.status.StatusMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.MsgTypeCat;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class StatusHandler {

    private final ControllerService controllerService;

    private final MessageSender messageSender;

    public StatusHandler(ControllerService controllerService, MessageSender messageSender) {
        this.controllerService = controllerService;
        this.messageSender = messageSender;
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processStatus(TabOutBase tabOutBase)
    {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabOutBase.getControllerId());

        if(signalInfoOp.isEmpty()){
            return;
        }

        //判定参数
        MsgType msgType = tabOutBase.getMsgType();
        Optional<StatusMqObject> statusMqObjectOp = StatusMqObject.getType(msgType.getCode());
        if(statusMqObjectOp.isEmpty() || statusMqObjectOp.get().objectId().isEmpty()){
            return;
        }

        boolean notify = false;
        int msg1 = (tabOutBase.getMsgType().getCode() >> 24) & 0xff;
        if(MsgTypeCat.CAT_STATUS.getCode() == msg1) {
            log.trace("收到信号机[状态]-[{}]-数据-{}", statusMqObjectOp.get().des(), tabOutBase);
            //获取推送标记,部分数据需要二次加工再推送
            notify = statusMqObjectOp.get().push();
        }

        if(notify){
            MqMessage mqMessage =
                    OpenLesMqUtils.buildPushMqMsg(signalInfoOp.get().getSignalId(), statusMqObjectOp.get().objectId(), tabOutBase);
            messageSender.sendNats(signalInfoOp.get().getSignalId(),
                    tabOutBase.getClass().getSimpleName().toLowerCase(), signalInfoOp.get().getNoArea(), signalInfoOp.get().getNoJunc(), mqMessage);

        }
    }

    /**
     * 向NATS系统发送组装的数据项
     * @param controllerId
     * @param statusMqObject
     * @param object
     */
    public void sendStatus2Nats(String controllerId, StatusMqObject statusMqObject, Object object){

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);

        if(signalInfoOp.isEmpty()){
            return;
        }

        //生成数据项
        MqMessage mqMessage =
                OpenLesMqUtils.buildPushMqMsg(signalInfoOp.get().getSignalId(), statusMqObject.objectId(), object);

        messageSender.sendNats(signalInfoOp.get().getSignalId(),
                object.getClass().getSimpleName().toLowerCase(), signalInfoOp.get().getNoArea(), signalInfoOp.get().getNoJunc(), mqMessage);

    }

}



