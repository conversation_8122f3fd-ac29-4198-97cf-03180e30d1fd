package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterGreenTimeTable implements DataIndexAble {
    // 相位绿间隔表号
    @NotNull(message = "{juncer.param.interGreenTimeTable.interGreenTimeTableNo.notNull}")
    @Range(min = 1, max = 4, message = "{juncer.param.interGreenTimeTable.interGreenTimeTableNo.range}")
    private Integer interGreenTimeTableNo;
    
    // 相位绿间隔配置列表
    @NotNull(message = "{juncer.param.interGreenTimeTable.interGreenTimes.notNull}")
    @Size(min = 1, max = 64, message = "{juncer.param.interGreenTimeTable.interGreenTimes.size}")
    @Valid
    private List<InterGreenTime> interGreenTimes;

    @Override
    public int getDataNo() {
        return interGreenTimeTableNo;
    }
}