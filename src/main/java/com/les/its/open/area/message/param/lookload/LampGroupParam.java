package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.LampGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 灯组参数信息
 */
@Data
public class LampGroupParam implements SgpTransAble {
    public static final String MqObjectId = "10003";

    /**
     * 灯组信息列表
     */
    @NotNull(message = "{basic.param.lampGroupParam.lampGroups.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.lampGroupParam.lampGroups.size}")
    @Valid
    private List<LampGroup> lampGroups;


    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 