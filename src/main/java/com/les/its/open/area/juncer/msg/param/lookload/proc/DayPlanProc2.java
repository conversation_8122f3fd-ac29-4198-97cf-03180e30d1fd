package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan;
import com.les.its.open.area.juncer.msg.param.lookload.dto.SegmentAction;
import com.les.its.open.area.juncer.msg.param.lookload.dto.SegmentParam;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class DayPlanProc2 implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_DAY_PLAN;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            DayPlan dayPlan = new DayPlan();
            objects.add(dayPlan);
            // 日计划编号
            dayPlan.setDayPlanNo(buf.readUnsignedByte() & 0xff);
            // 子路口号
            dayPlan.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            // 时段个数
            dayPlan.setSegmentNum(buf.readUnsignedByte() & 0xff);
            // 时段参数列表
            List<SegmentParam> segmentParams = new ArrayList<>();
            dayPlan.setSegmentParams(segmentParams);
            for (int i = 0; i < 48; i++) {
                SegmentParam segmentParam = new SegmentParam();
                //有效数据添加到列表中，但是数据项依然需要设置
                if(i < dayPlan.getSegmentNum()) {
                    segmentParams.add(segmentParam);
                }
                segmentParam.setStartTime(buf.readUnsignedShortLE() & 0xffff);
                segmentParam.setPlanNo(buf.readUnsignedByte() & 0xff);
                segmentParam.setControlMode(buf.readUnsignedByte() & 0xff);
                segmentParam.setCoordCorrectionMode(buf.readUnsignedByte() & 0xff);
                segmentParam.setCoordDirection(buf.readUnsignedByte() & 0xff);
                segmentParam.setActionNum(0);

                List<SegmentAction> actions = new ArrayList<>();
                segmentParam.setSegmentActions(actions);
            }

            //动作链参数
            int countAction  =  buf.readUnsignedByte() & 0xff;
            int segmentSeq = 0;
            int functionNo = 0;
            int param1 = 0;
            int param2 = 0;
            for (int i = 0; i < 64; i++) {
                segmentSeq = buf.readUnsignedByte() & 0xff;
                functionNo = buf.readUnsignedByte() & 0xff;
                param1 = buf.readUnsignedShortLE() & 0xffff;
                param2 = buf.readUnsignedShortLE() & 0xffff;

                if (i < countAction && segmentSeq > 0 && segmentSeq <= dayPlan.getSegmentParams().size()) {
                    if (functionNo > 0) {
                        SegmentParam segmentParam = dayPlan.getSegmentParams().get(segmentSeq - 1);
                        segmentParam.setActionNum(segmentParam.getActionNum() + 1);
                        segmentParam.getSegmentActions().add(
                                SegmentAction.builder()
                                        .functionNo(functionNo)
                                        .param1(param1)
                                        .param2(param2)
                                        .build()
                        );
                    }
                }
            }


            // 日计划名称
            dayPlan.setName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());
        }

        buf.release();
        return Optional.of(objects);
    }

    @Data
    @AllArgsConstructor
    private class TmpSegmentAction{
        int segmentSeq;
        int functionNo;
        int param1;
        int param2;
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof DayPlan)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            DayPlan dayPlan
                    = (DayPlan)objects.get(i);

            // 日计划编号
            buf.writeByte(dayPlan.getDayPlanNo());
            // 子路口号
            buf.writeByte(dayPlan.getCrossingSeqNo());
            // 时段个数
            buf.writeByte(dayPlan.getSegmentNum());
            // 时段参数列表
            List<SegmentParam> segmentParams = dayPlan.getSegmentParams();
            for (int k = 0; k < 48; k++) {
                if(k < segmentParams.size()){
                    SegmentParam segmentParam = segmentParams.get(k);
                    buf.writeShortLE(segmentParam.getStartTime());
                    buf.writeByte(segmentParam.getPlanNo());
                    buf.writeByte(segmentParam.getControlMode());
                    buf.writeByte(segmentParam.getCoordCorrectionMode());
                    buf.writeByte(segmentParam.getCoordDirection());
                }else {
                    buf.writeShortLE(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                }
            }

            //迟起早断参数
            {
                List<TmpSegmentAction> tmpSegmentActions = new ArrayList<>();
                for (int k = 0; k < segmentParams.size(); k++) {
                    SegmentParam segmentParam = segmentParams.get(k);
                    List<SegmentAction> actions = segmentParam.getSegmentActions();
                    for (int j = 0; j < actions.size(); j++) {
                        SegmentAction action = actions.get(j);
                        tmpSegmentActions.add(new TmpSegmentAction(k + 1, action.getFunctionNo(), action.getParam1(), action.getParam2()));
                    }
                }
                buf.writeByte(Math.min(tmpSegmentActions.size(), 64));
                for (int j = 0; j < 64; j++) {
                    if(j < tmpSegmentActions.size()) {
                        TmpSegmentAction tmpSegmentAction = tmpSegmentActions.get(j);
                        buf.writeByte(tmpSegmentAction.getSegmentSeq());
                        buf.writeByte(tmpSegmentAction.getFunctionNo());
                        buf.writeShortLE(tmpSegmentAction.getParam1());
                        buf.writeShortLE(tmpSegmentAction.getParam2());
                    }else{
                        buf.writeByte(0x00);
                        buf.writeByte(0x00);
                        buf.writeShortLE(0x00);
                        buf.writeShortLE(0x00);
                    }
                }
            }

            //方案名称
            {
                byte[] name = new byte[64];
                byte[] bytes = dayPlan.getName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 3 + 48 * (6) + 1 + 64 * (6) + 64;
    }

    @Override
    public Class dataClazz() {
        return DayPlan.class;
    }
}
