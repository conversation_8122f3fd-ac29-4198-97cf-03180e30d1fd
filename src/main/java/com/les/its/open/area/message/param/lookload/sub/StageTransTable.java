package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraint;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 阶段转移表
 */
@Data
public class StageTransTable {
    /**
     * 阶段转移表编号
     * [1,4]
     */
    @NotNull(message = "{basic.param.stageTransTable.stageTransTableNo.notNull}")
    @Range(min = 1, max = 4, message = "{basic.param.stageTransTable.stageTransTableNo.range}")
    private Integer stageTransTableNo;

    /**
     * 阶段转移参数
     */
    @NotNull(message = "{basic.param.stageTransTable.stageTransConstraint.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.stageTransTable.stageTransConstraint.size}")
    @Valid
    private List<StageTransitionConstraint> stageTransConstraint;
} 