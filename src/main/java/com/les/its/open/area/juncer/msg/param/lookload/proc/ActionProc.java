package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Action;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ActionProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_ACTION;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Action action = new Action();
            objects.add(action);
            // 条件动作编号
            action.setActionNo(buf.readUnsignedByte() & 0xff);
            // 变量类型
            action.setParamType(buf.readUnsignedByte() & 0xff);
            // 变量编号
            action.setParamNo(buf.readUnsignedByte() & 0xff);
            // 功能编号
            action.setFunctionNo(buf.readUnsignedByte() & 0xff);
            // 参数1
            action.setParam1(buf.readUnsignedShortLE() & 0xffff);
            // 参数2
            action.setParam2(buf.readUnsignedShortLE() & 0xffff);
            // 动作名称
            action.setActionName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Action)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Action action
                    = (Action)objects.get(i);

            // 条件动作编号
            buf.writeByte(action.getActionNo() & 0xff);
            // 变量类型
            buf.writeByte(action.getParamType() & 0xff);
            // 变量编号
            buf.writeByte(action.getParamNo() & 0xff);
            // 功能编号
            buf.writeByte(action.getFunctionNo() & 0xff);
            // 参数1
            buf.writeShortLE(action.getParam1() & 0xffff);
            // 参数2
            buf.writeShortLE(action.getParam2() & 0xffff);
            // 条件计算名称
            {
                byte[] name = new byte[64];
                byte[] bytes = action.getActionName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 8 + 64;
    }

    @Override
    public Class dataClazz() {
        return Action.class;
    }
}
