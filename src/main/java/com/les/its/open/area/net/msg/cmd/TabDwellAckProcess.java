package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabDwell;
import com.les.its.open.area.juncer.msg.cmd.TabDwellAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabDwellAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_DWELL_ACK;
    }

    @Override
    public int oneItemSize() {
        return  (2 + 1 + 2 + 8);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabDwellAck tabDwellAck = new TabDwellAck();
            tabDwellAck.setAck(buf.readUnsignedByte() & 0xff);
            tabDwellAck.setReason(buf.readUnsignedByte() & 0xff);

            TabDwell tabDwell = new TabDwell();
            tabDwellAck.setTabDwell(tabDwell);
            tabDwell.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            tabDwell.setDuration(buf.readUnsignedShortLE());
            tabDwell.setPhasesFlag(buf.readLongLE());

            Long phaseId = tabDwell.getPhasesFlag();
            List<Integer> phases = new ArrayList();
            for (int i = 0; i < 64; i++) {
                if(((phaseId >> i) & (0x01L)) == 0x01){
                    phases.add(i + 1);
                }
            }

            tabDwell.setPhases(phases);

            buf.release();

            return Optional.of(tabDwellAck);
        }

        return Optional.empty();
    }

}
