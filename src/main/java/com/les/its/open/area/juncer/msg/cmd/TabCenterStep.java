package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabCenterStep extends TabInBase implements NeedAck {

    @Range(min = 1, max = 8, message = "子路口号范围[1,8]")
    private int crossingSeqNo;

    @DigitsInSet(acceptedValues = {0,1}, message = "步进模式必须是0或者1")
    private int stepMode;

    private int duration;

    @DigitsInSet(acceptedValues = {2}, message = "特征值必须是2")
    private int character;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_CENTER_STEP.getCode());
    }
}
