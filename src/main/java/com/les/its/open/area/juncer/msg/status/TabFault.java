package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.status.sub.FaultAction;
import com.les.its.open.area.net.msg.status.sub.FaultStatus;
import com.les.its.open.area.net.msg.status.sub.FaultType;
import com.les.its.open.utils.UtcTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 故障-其他
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabFault extends TabOutBase {

    /**
     * 故障事件发生时间
     */
    private long faultTime;

    /**
     * 故障状态
     */
    private int faultStatus;
    
    /**
     * 故障动作
     */
    private int faultAction;
    
    /**
     * 故障类型
     */
    private int faultType;
    
    /**
     * 故障详情
     */
    private List<Integer> faultDetails;

    /**
     * 置1bit的数据
     */
    private List<Integer> faultDetailBits;


    private String description;

    public void genDescription() {
        TabFault tabFault = this;
        LocalDateTime localDateTimeGen = UtcTimeUtils.convert2LocalDateTime(tabFault.getFaultTime());
        FaultStatus faultStatusEnum = FaultStatus.getByCode(tabFault.getFaultStatus());
        FaultType faultTypeEnum = FaultType.getByCode(tabFault.getFaultType());
        FaultAction faultActionEnum = FaultAction.getByCode(tabFault.getFaultAction());

        long data = 0L;
        for (int i = 0; i < 8 && i < tabFault.getFaultDetails().size(); i++) {
            data |= ( (long)tabFault.getFaultDetails().get(i) << (i * 8));
        }

        for (int i = 0; i < 64; i++) {
            if(((data >> i) & 0x01) == 0x01){
                faultDetailBits.add(i + 1);
            }
        }

        description = String.format("故障时间:%s, " +
                        "状态:%d (%s), 动作:%d (%s), 类型:%d (%s), " +
                        "详情:[%s], 置位:[%s]", localDateTimeGen,
                tabFault.getFaultStatus(), faultStatusEnum.getDescription(),
                tabFault.getFaultAction(), faultActionEnum.getDescription(),
                tabFault.getFaultType(), faultTypeEnum.getDescription(),
                tabFault.getFaultDetails(), faultDetailBits);
    }

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        controllerAgent.getTabFaults().add(this);

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }

} 