package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreenInfo;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.PhaseParam;
import com.les.its.open.area.message.param.lookload.sub.PhaseParamInfo;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class PhaseParamHandler  implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public PhaseParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_PhaseParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return PhaseParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看相位信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_PHASE, 1, ParamMsgType.PARAM_PHASE.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        // 调看限制绿信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_PHASE_TABLE_LIMIT_GREEN, 1, 1, signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            PhaseParam phaseParam = new PhaseParam();
            //设置信号机编号
            phaseParam.setSignalControllerID(requestMessage.getSignalControllerID());

            //相位信息
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.Phase>> phases
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_PHASE);
            if(phases.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_PHASE);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), phases.get());


            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreens> phaseTableLimitGreens
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_PHASE_TABLE_LIMIT_GREEN);
            if(phases.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_PHASE_TABLE_LIMIT_GREEN);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), phaseTableLimitGreens.get());


            List<PhaseParamInfo> phaseParams = new ArrayList<>();
            phaseParam.setPhaseParams(phaseParams);
            phases.get().forEach(
                    phase -> {
                        PhaseParamInfo phaseInfo = new PhaseParamInfo();
                        phaseInfo.setPhaseNo(phase.getPhaseNo());

                        //转换nats有效数据项
                        phaseInfo.setPhaseLightsGroups(phase.getPhaseLightsGroups());
                        phaseInfo.setAppearance(phase.getAppearance());
                        phaseInfo.setTermination(phase.getTermination());
                        phaseInfo.setPhaseName(phase.getPhaseName());
                        phaseInfo.setLampSequenceNo(phase.getLampSequenceNo());
                        phaseInfo.setAssocPhase(phase.getAssocPhase());
                        phaseParams.add(phaseInfo);
                    }
            );


            //设置关联的限制绿信息
            phaseParam.getPhaseParams().forEach(
                phase -> {
                    List<PhaseTableLimitGreenInfo> phaseTableLimitGreenInfoInfos = phaseTableLimitGreens.get().getPhaseTableLimitGreenInfoInfos();
                    if(phase.getPhaseNo() > 0 && phase.getPhaseNo() <= phaseTableLimitGreenInfoInfos.size()){
                        PhaseTableLimitGreenInfo phaseTableLimitGreenInfo = phaseTableLimitGreenInfoInfos.get(phase.getPhaseNo() - 1);
                        phase.setMinGreenTime(phaseTableLimitGreenInfo.getMinGreenTime1());
                        phase.setMaxGreenTime1(phaseTableLimitGreenInfo.getMaxGreenTime1());
                        phase.setMaxGreenTime2(phaseTableLimitGreenInfo.getMaxGreenTime2());
                        phase.setMaxGreenTime3(phaseTableLimitGreenInfo.getMaxGreenTime3());
                        phase.setMaxGreenTime4(phaseTableLimitGreenInfo.getMaxGreenTime4());
                        phase.setMaxGreenTime5(phaseTableLimitGreenInfo.getMaxGreenTime5());
                        phase.setMaxGreenTime6(phaseTableLimitGreenInfo.getMaxGreenTime6());
                        phase.setMaxGreenTime7(phaseTableLimitGreenInfo.getMaxGreenTime7());
                        phase.setMaxGreenTime8(phaseTableLimitGreenInfo.getMaxGreenTime8());
                    }
                }
            );

            //应答参数
            datas.add(phaseParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {

        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        PhaseParam phaseParam = jsonObject.toJavaObject(PhaseParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(phaseParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, phaseParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //相位信息
        {

            List<PhaseParamInfo> phaseParams = phaseParam.getPhaseParams();
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.Phase> phases = new ArrayList<>();
            phaseParams.forEach(phaseParamInfo -> {
                com.les.its.open.area.juncer.msg.param.lookload.dto.Phase phase
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.Phase();
                phase.setPhaseNo(phaseParamInfo.getPhaseNo());

                //转换成信号机有效数据项
                phase.setPhaseLightsGroups(phaseParamInfo.getPhaseLightsGroups());
                phase.setAppearance(phaseParamInfo.getAppearance());
                phase.setTermination(phaseParamInfo.getTermination());
                phase.setPhaseName(phaseParamInfo.getPhaseName());
                phase.setLampSequenceNo(phaseParamInfo.getLampSequenceNo());
                phase.setAssocPhase(phaseParamInfo.getAssocPhase());
                phases.add(phase);
            });

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_PHASE, phases, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //限制绿信息
        {
            List<PhaseParamInfo> phaseParams = phaseParam.getPhaseParams();
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreenInfo> phaseTableLimitGreenInfos
                    = new ArrayList<>();
            phaseParams.forEach(phaseParamInfo -> {
                com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreenInfo phaseTableLimitGreenInfo
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreenInfo();
                phaseTableLimitGreenInfo.setMinGreenTime1(phaseParamInfo.getMinGreenTime());
                phaseTableLimitGreenInfo.setMinGreenTime2(0);
                phaseTableLimitGreenInfo.setMinGreenTime3(0);
                phaseTableLimitGreenInfo.setMinGreenTime4(0);
                phaseTableLimitGreenInfo.setMaxGreenTime1(phaseParamInfo.getMaxGreenTime1());
                phaseTableLimitGreenInfo.setMaxGreenTime2(phaseParamInfo.getMaxGreenTime2());
                phaseTableLimitGreenInfo.setMaxGreenTime3(phaseParamInfo.getMaxGreenTime3());
                phaseTableLimitGreenInfo.setMaxGreenTime4(phaseParamInfo.getMaxGreenTime4());
                phaseTableLimitGreenInfo.setMaxGreenTime5(phaseParamInfo.getMaxGreenTime5());
                phaseTableLimitGreenInfo.setMaxGreenTime6(phaseParamInfo.getMaxGreenTime6());
                phaseTableLimitGreenInfo.setMaxGreenTime7(phaseParamInfo.getMaxGreenTime7());
                phaseTableLimitGreenInfo.setMaxGreenTime8(phaseParamInfo.getMaxGreenTime8());
                phaseTableLimitGreenInfos.add(phaseTableLimitGreenInfo);
            });

            //补全64个参数
            if(phaseTableLimitGreenInfos.size() < 64){
                for(int i = phaseTableLimitGreenInfos.size(); i < 64; i++){
                    com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreenInfo phaseTableLimitGreenInfo
                            = new com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreenInfo();
                    phaseTableLimitGreenInfo.setMinGreenTime1(8);
                    phaseTableLimitGreenInfo.setMinGreenTime2(0);
                    phaseTableLimitGreenInfo.setMinGreenTime3(0);
                    phaseTableLimitGreenInfo.setMinGreenTime4(0);
                    phaseTableLimitGreenInfo.setMaxGreenTime1(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime2(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime3(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime4(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime5(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime6(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime7(80);
                    phaseTableLimitGreenInfo.setMaxGreenTime8(80);
                    phaseTableLimitGreenInfos.add(phaseTableLimitGreenInfo);
                }
            }

            //生成加载参数
            com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreens phaseTableLimitGreens
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseTableLimitGreens();
            phaseTableLimitGreens.setPhaseTableNo(1);
            phaseTableLimitGreens.setPhaseTableLimitGreenInfoInfos(phaseTableLimitGreenInfos);
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_PHASE_TABLE_LIMIT_GREEN,
                    phaseTableLimitGreens, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
