package com.les.its.open.area.message.handler.status;

import com.les.its.open.area.juncer.msg.status.TabPhaseStatus;
import com.les.its.open.area.message.param.status.StatusMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.service.WsMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

//由信号机发生变化时推送
@Service
@Slf4j
public class TabPhaseStatusHandler {

    private final ControllerService controllerService;

    private final CrossingService crossingService;

    private final StatusHandler statusHandler;

    private final WsMessageService wsMessageService;

    private final boolean checkPhaseStatus = false;

    public TabPhaseStatusHandler(ControllerService controllerService, CrossingService crossingService,
                                 StatusHandler statusHandler, WsMessageService wsMessageService) {
        this.controllerService = controllerService;
        this.crossingService = crossingService;
        this.statusHandler = statusHandler;
        this.wsMessageService = wsMessageService;
    }

    private Map<String, TabPhaseStatus> phaseStatusMap = new ConcurrentHashMap<>();


    public String getKey(TabPhaseStatus tabPhaseStatus){
        return  tabPhaseStatus.getControllerId() + "###" + tabPhaseStatus.getCrossingSeqNo();
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processTabPhaseStatus(TabPhaseStatus tabPhaseStatus) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabPhaseStatus.getControllerId());

        if (signalInfoOp.isEmpty()) {
            return;
        }

        //ws界面发送
        wsMessageService.recvPhaseStatus(tabPhaseStatus.getControllerId(), tabPhaseStatus);

        if(checkPhaseStatus) {
            //判定相位状态是否发生变化
            String key = getKey(tabPhaseStatus);
            TabPhaseStatus tabPhaseStatusOld = phaseStatusMap.get(key);
            boolean needNotify = false;
            if (tabPhaseStatusOld == null) {
                needNotify = true;

            } else {
                needNotify = tabPhaseStatus.isChg(tabPhaseStatusOld);
            }

            if (needNotify) {
                sendTabPhaseStatus(tabPhaseStatus.getControllerId(), tabPhaseStatus);
            }

            phaseStatusMap.put(key, tabPhaseStatus);
        }
    }

    /**
     * 发送路口实时相位
     * @param controllerId
     */
    public void sendTabPhaseStatus(String controllerId, TabPhaseStatus tabPhaseStatus){
        statusHandler.sendStatus2Nats(controllerId, StatusMqObject.TAB_PHASE_STATUS, tabPhaseStatus);
    }


}
