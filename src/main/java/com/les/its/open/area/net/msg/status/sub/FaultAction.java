package com.les.its.open.area.net.msg.status.sub;

/**
 * 故障动作枚举
 */
public enum FaultAction {
    
    /**
     * 无动作动作
     */
    SWITCH_NULL(0x00, "无动作动作", "Switch_Null"),
    
    /**
     * 切换到黄闪
     */
    SWITCH_TO_FLASH(0x10, "切换到黄闪", "Switch_To_Flash"),
    
    /**
     * 切换到关灯
     */
    SWITCH_TO_OFF(0x20, "切换到关灯", "Switch_To_Off"),
    
    /**
     * 切换到全红
     */
    SWITCH_TO_RED(0x30, "切换到全红", "Switch_To_Red"),
    
    /**
     * 切换到本地定周期
     */
    SWITCH_TO_LOCAL_FIX_CYCLE(0x40, "切换到本地定周期", "Switch_To_Local_FixCycle"),
    
    /**
     * 切换到本地协调
     */
    SWITCH_TO_LOCAL_COORDINATION(0x50, "切换到本地协调", "Switch_To_Local_Coordination"),
    
    /**
     * 切换到本地感应
     */
    SWITCH_TO_LOCAL_VA(0x60, "切换到本地感应", "Switch_To_Local_Va"),

    UNKNOWN(0xFFFF, "未知动作", "Unknown_Action");
    
    private final int code;
    private final String description;
    private final String englishName;
    
    FaultAction(int code, String description, String englishName) {
        this.code = code;
        this.description = description;
        this.englishName = englishName;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    /**
     * 根据故障动作代码获取故障动作类型
     * @param code 故障动作代码
     * @return 故障动作类型枚举，如果未找到返回null
     */
    public static FaultAction getByCode(int code) {
        for (FaultAction faultAction : values()) {
            if (faultAction.getCode() == code) {
                return faultAction;
            }
        }
        return UNKNOWN;
    }
    
    @Override
    public String toString() {
        return String.format("FaultAction{code=0x%02X, description='%s', englishName='%s'}", 
                           code, description, englishName);
    }
}
