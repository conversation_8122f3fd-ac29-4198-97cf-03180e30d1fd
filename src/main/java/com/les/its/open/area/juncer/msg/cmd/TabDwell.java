package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 指定相位控制
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabDwell extends TabInBase implements NeedAck {

    /**
     * 子路口号
     */
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号范围[1,8]")
    private Integer crossingSeqNo;

    /**
     * 控制时间
     */
    @NotNull(message = "控制时间不能为空")
    private Integer duration;

    /**
     * 控制标记
     */
    private Long phasesFlag;

    @NotNull(message = "控制相位不能为空")
    @DiscreteValuesList(min = 1, max = 64, message = "控制相位有效范围是[1,64]")
    private List<Integer> phases;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_DWELL.getCode());
    }
}