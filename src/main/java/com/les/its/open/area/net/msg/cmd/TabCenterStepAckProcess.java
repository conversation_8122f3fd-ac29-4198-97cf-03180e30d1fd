package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabCenterStep;
import com.les.its.open.area.juncer.msg.cmd.TabCenterStepAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabCenterStepAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_CENTER_STEP_ACK;
    }

    @Override
    public int oneItemSize() {
        return (2 + 5 );
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabCenterStepAck tabCenterStepAck = new TabCenterStepAck();
            tabCenterStepAck.setAck(buf.readUnsignedByte() & 0xff);
            tabCenterStepAck.setReason(buf.readUnsignedByte() & 0xff);

            TabCenterStep tabCenterStep = new TabCenterStep();
            tabCenterStepAck.setTabCenterStep(tabCenterStep);
            tabCenterStep.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            tabCenterStep.setCharacter(buf.readUnsignedByte() & 0xff);
            tabCenterStep.setStepMode(buf.readUnsignedByte() & 0xff);
            tabCenterStep.setDuration(buf.readUnsignedShortLE());

            buf.release();

            return Optional.of(tabCenterStepAck);
        }

        return Optional.empty();
    }

}
