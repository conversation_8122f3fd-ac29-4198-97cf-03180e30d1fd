package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncompatiblePhases implements DataIndexAble {
    // 相位冲突配置表列表
    @NotNull(message = "{juncer.param.incompatiblePhases.incompatiblePhases.notNull}")
    @Size(min = 1, max = 64, message = "{juncer.param.incompatiblePhases.incompatiblePhases.size}")
    @Valid
    private List<IncompatiblePhase> incompatiblePhases;

    @Override
    public int getDataNo() {
        return 1;
    }
}