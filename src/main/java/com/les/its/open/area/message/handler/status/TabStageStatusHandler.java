package com.les.its.open.area.message.handler.status;

import com.les.its.open.area.juncer.msg.status.TabStageStatus;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.service.WsMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class TabStageStatusHandler {

    private final ControllerService controllerService;

    private final CrossingService crossingService;

    private final StatusHandler statusHandler;

    private final WsMessageService wsMessageService;

    public TabStageStatusHandler(ControllerService controllerService, CrossingService crossingService,
                                 StatusHandler statusHandler, WsMessageService wsMessageService) {
        this.controllerService = controllerService;
        this.crossingService = crossingService;
        this.statusHandler = statusHandler;
        this.wsMessageService = wsMessageService;
    }

    private Map<String, TabStageStatus> stageStatusMap = new ConcurrentHashMap<>();


    public String getKey(TabStageStatus tabStageStatus){
        return  tabStageStatus.getControllerId() + "###" ;
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processTabStageStatus(TabStageStatus tabStageStatus) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabStageStatus.getControllerId());

        if (signalInfoOp.isEmpty()) {
            return;
        }

        //WS数据发送
        wsMessageService.recvStageStatus(tabStageStatus.getControllerId(), tabStageStatus);

        //判定相位状态是否发生变化
        String key = getKey(tabStageStatus);
        TabStageStatus tabStageStatusOld = stageStatusMap.get(key);
        boolean needNotify = false;
        if(tabStageStatusOld == null){
            needNotify = true;

        }else{
            needNotify = tabStageStatus.isChg(tabStageStatusOld);
        }

        if(needNotify){
            sendTabStageStatus(tabStageStatus.getControllerId(), tabStageStatus);
        }

        stageStatusMap.put(key, tabStageStatus);

    }

    /**
     * 发送路口实时阶段
     * @param controllerId
     */
    public void sendTabStageStatus(String controllerId, TabStageStatus tabStageStatus){
        //statusHandler.sendStatus2Nats(controllerId, StatusMqObject.TAB_STAGE_STATUS, tabStageStatus);
    }




}
