package com.les.its.open.area.message.param.cmd;

import java.util.Arrays;
import java.util.Optional;

public enum CmdMqObject {

    TAB_CANCEL_CMD(0x04010000,  "14002", "恢复本地控制"),
    TAB_CMD(0x04020000, "14003","一般控制命令"),
    TAB_INQUIRE(0x04030000, "14004","设备状态查询"),
    TAB_DWELL(0x04040000, "14005","相位/灯组驻留"),
    TAB_SET_STAGE(0x04050000, "14006","指定相位阶段控制"),
    TAB_SPECIAL_MODE(0x04060000, "14007","特殊控制方式"),
    TAB_REAL_TIME_OPTIMIZE(0x04070000, "14008","实时优化控制"),
    TAB_PLAN_CMD(0x04080000, "14009","方案控制"),
    TAB_DETECTOR_CONTROL_GROUP_ACTUATION(0x04090000, "14010","检测器控制组"),
    TAB_DETECTOR_RESET(0x040A0000, "14011","检测器重置控制组"),
    TAB_ACTIVATED_PHASE_CONTROL(0x040B0000, "14012","感应需求相位控制"),
    TAB_ACTIVATED_PHASE_CONTROL2(0x040C0000, "14013", "感应需求相位控制"),
    TAB_STAGE_CALL(0x040D0000, "14014","阶段软件需求"),
    TAB_STAGE_SHIELD(0x040E0000, "14015","屏蔽控制"),
    TAB_PROHIBIT_CMD(0x040F0000, "14016","禁止控制"),
    TAB_EMERGENCY_PRIORITY_CMD(0x04100000, "14017","紧急优先控制"),
    TAB_EMERGENCY_PRIORITY_DISABLE(0x04110000, "14018","紧急优先屏蔽控制"),
    TAB_TRANSACTION_CMD(0x04120000, "14001","事务交易控制"),
    TAB_CENTER_STEP(0x04130000, "14019", "系统步进控制" );

    private int value;
    private String objectId;
    private String des;

    CmdMqObject(int value, String objectId, String des){
        this.value = value;
        this.objectId = objectId;
        this.des = des;
    }

    public int value(){
        return this.value;
    }

    public String objectId() {
        return this.objectId;
    }

    public String des(){
        return this.des;
    }

    public static Optional<CmdMqObject> getType(int orgValue) {

        return Arrays.stream(CmdMqObject.values())
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();
    }


    public static Optional<CmdMqObject> getTypeByObjectId(String objectId) {
        return Arrays.stream(CmdMqObject.values())
                .filter(messageType -> messageType.objectId().equals(objectId))
                .findAny();
    }

}
