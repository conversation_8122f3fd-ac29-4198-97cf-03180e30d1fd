package com.les.its.open.area.juncer.msg.status;

import com.google.common.collect.Iterables;
import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 实时阶段状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabStageStatus extends TabOutBase {
    /**
     * 阶段状态
     */
    private List<Integer> stageStatus;
    
    /**
     * 阶段需运行时间
     */
    private List<Integer> needRunningTimes;
    
    /**
     * 阶段已运行时间
     */
    private List<Integer> runningTimes;


    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.trace("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        controllerAgent.setTabStageStatus(this);

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }

    public boolean isChg(TabStageStatus tabStageStatus){

        // 比较 stageStatus 是否发生变化
        if (!Iterables.elementsEqual(this.stageStatus, tabStageStatus.getStageStatus())) {
            return true;
        }

        // 比较 needRunningTimes 是否发生变化
        if (!Iterables.elementsEqual(this.needRunningTimes, tabStageStatus.getNeedRunningTimes())) {
             return true;
        }

        return false;
    }
} 