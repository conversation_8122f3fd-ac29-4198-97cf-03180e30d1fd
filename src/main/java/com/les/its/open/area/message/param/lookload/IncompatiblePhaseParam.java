package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.IncompatiblePhase;

import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 相位冲突参数
 */
@Data
public class IncompatiblePhaseParam implements SgpTransAble {
    public static final String MqObjectId = "10009";

    /**
     * 相位冲突表
     */
    @NotNull(message = "{basic.param.incompatiblePhase.incompatiblePhases.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.incompatiblePhase.incompatiblePhases.size}")
    @Valid
    private List<IncompatiblePhase> incompatiblePhases;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 