package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan;
import com.les.its.open.area.juncer.msg.param.lookload.dto.SegmentAction;
import com.les.its.open.area.juncer.msg.param.lookload.dto.SegmentParam;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

//@Component
@Slf4j
public class DayPlanProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_DAY_PLAN;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            DayPlan dayPlan = new DayPlan();
            objects.add(dayPlan);
            // 日计划编号
            dayPlan.setDayPlanNo(buf.readUnsignedByte() & 0xff);
            // 子路口号
            dayPlan.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            // 时段个数
            dayPlan.setSegmentNum(buf.readUnsignedByte() & 0xff);
            // 时段参数列表
            List<SegmentParam> segmentParams = new ArrayList<>();
            dayPlan.setSegmentParams(segmentParams);
            for (int i = 0; i < 48; i++) {
                SegmentParam segmentParam = new SegmentParam();
                //有效数据添加到列表中，但是数据项依然需要设置
                if(i < dayPlan.getSegmentNum()) {
                    segmentParams.add(segmentParam);
                }
                segmentParam.setStartTime(buf.readUnsignedShortLE() & 0xffff);
                segmentParam.setPlanNo(buf.readUnsignedByte() & 0xff);
                segmentParam.setControlMode(buf.readUnsignedByte() & 0xff);
                segmentParam.setCoordCorrectionMode(buf.readUnsignedByte() & 0xff);
                segmentParam.setCoordDirection(buf.readUnsignedByte() & 0xff);
                segmentParam.setActionNum(buf.readUnsignedByte() & 0xff);

                List<SegmentAction> actions = new ArrayList<>();
                segmentParam.setSegmentActions(actions);
                for (int j = 0; j < 16; j++) {
                    SegmentAction action = new SegmentAction();
                    if(j < segmentParam.getActionNum()) {
                        actions.add(action);
                    }
                    action.setFunctionNo(buf.readUnsignedByte() & 0xff);
                    action.setParam1(buf.readUnsignedShortLE() );
                    action.setParam2(buf.readUnsignedShortLE() );
                }
            }
            // 日计划名称
            dayPlan.setName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof DayPlan)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            DayPlan dayPlan
                    = (DayPlan)objects.get(i);

            // 日计划编号
            buf.writeByte(dayPlan.getDayPlanNo());
            // 子路口号
            buf.writeByte(dayPlan.getCrossingSeqNo());
            // 时段个数
            buf.writeByte(dayPlan.getSegmentNum());
            // 时段参数列表
            List<SegmentParam> segmentParams = dayPlan.getSegmentParams();
            for (int k = 0; k < 48; k++) {
                if(k < segmentParams.size()){
                    SegmentParam segmentParam = segmentParams.get(k);
                    buf.writeShortLE(segmentParam.getStartTime());
                    buf.writeByte(segmentParam.getPlanNo());
                    buf.writeByte(segmentParam.getControlMode());
                    buf.writeByte(segmentParam.getCoordCorrectionMode());
                    buf.writeByte(segmentParam.getCoordDirection());
                    buf.writeByte(segmentParam.getActionNum());
                    List<SegmentAction> actions = segmentParam.getSegmentActions();
                    for (int j = 0; j < 16; j++) {
                        if(j < actions.size()) {
                            SegmentAction action = actions.get(j);
                            buf.writeByte(action.getFunctionNo());
                            buf.writeShortLE(action.getParam1());
                            buf.writeShortLE(action.getParam2());
                        }else{
                            buf.writeByte(0x00);
                            buf.writeShortLE(0x00);
                            buf.writeShortLE(0x00);
                        }
                    }
                }else {
                    buf.writeShortLE(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    for (int j = 0; j < 16; j++) {
                        buf.writeByte(0x00);
                        buf.writeShortLE(0x00);
                        buf.writeShortLE(0x00);
                    }
                }
            }

            //方案名称
            {
                byte[] name = new byte[64];
                byte[] bytes = dayPlan.getName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 3 + 48 * (7 + 16 * (5)) + 64;
    }

    @Override
    public Class dataClazz() {
        return DayPlan.class;
    }
}
