package com.les.its.open.event;


import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.protocol.common.message.AbstractProtocolMessage;
import com.les.its.open.protocol.common.utils.MessageProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @ClassName: AsynMessageProcess
 * @Description: 总线数据处理，接收报文发送的数据
 * @Author: king
 * @CreateDate: 2018/12/3 19:25
 */
@Component
@Slf4j
public class AsynMessageProcess extends AbstractSummary {

    private final MessageProcessUtils messageProcessUtils;

    public AsynMessageProcess(MessageProcessUtils messageProcessUtils) {
        this.messageProcessUtils = messageProcessUtils;
    }

    /**
     * 接收netty分包之后的数据包，进行报文的转换
     *
     * @param protocolMessage
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void consumeMessage(AbstractProtocolMessage protocolMessage) {

        boolean result = messageProcessUtils.processMessage(protocolMessage);

        if (result) {
            long dataLong = getAtomicSuccessLong().incrementAndGet();
            log.trace("success decode message [{}] AbstractProtocolMessage {}", dataLong, Thread.currentThread().getName());
        } else {
            long errorLong = getAtomicErrorLong().incrementAndGet();
            log.error("AbstractProtocolMessage decode error, already error/total decoded [{}/{}]", errorLong, errorLong + getAtomicSuccessLong().get());
        }
    }


}
