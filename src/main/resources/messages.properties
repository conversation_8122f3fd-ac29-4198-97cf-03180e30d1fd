# 基础参数验证消息
# 区域编号
basic.param.noArea.notNull=区域编号不能为空
basic.param.noArea.range=区域编号有效范围是[0,39]

# 路口编号
basic.param.noJunc.notNull=路口编号不能为空
basic.param.noJunc.range=路口编号有效范围是[0,499]

# 信号机型
basic.param.type.notNull=信号机型不能为空

# 出厂日期
basic.param.productionDate.notNull=出厂日期不能为空

# 经度
basic.param.lon.notNull=经度不能为空

# 纬度
basic.param.lat.notNull=纬度不能为空

# 通信参数匹配
basic.param.cmmType.match=通信类型与IP信息不匹配

# 控制路口数
basic.param.controlledJunctionNum.notNull=控制路口数不能为空
basic.param.controlledJunctionNum.range=控制路口有效范围是[1,8]

# 安装路口
basic.param.installIntersection.notNull=安装路口不能为空

# 通讯模式
basic.param.ipEnabled.notNull=通讯模式不能为空
basic.param.ipEnabled.range=通讯模式有效范围是[0,2]

# MAC地址
basic.param.macAddress.notNull=信号机mac地址不能为空
basic.param.macAddress.invalid=无效的MAC地址

# IPv4信息
basic.param.ipv4.notNull=信号机ipv4信息不能为空
basic.param.ipv4.ip.notNull=ipv4地址不能为空
basic.param.ipv4.ip.invalid=必须是有效的IPv4地址
basic.param.ipv4.mask.notNull=子网掩码不能为空
basic.param.ipv4.mask.invalid=必须是有效的子网掩码
basic.param.ipv4.gateway.notNull=网关不能为空
basic.param.ipv4.gateway.invalid=必须是有效的网关

# IPv6信息
basic.param.ipv6.notNull=信号机ipv6信息不能为空
basic.param.ipv6.ip.notNull=ipv6地址不能为空
basic.param.ipv6.ip.invalid=必须是有效的IPv6地址
basic.param.ipv6.mask.notNull=ipv6子网掩码不能为空
basic.param.ipv6.mask.invalid=必须是有效的IPv6子网掩码
basic.param.ipv6.gateway.notNull=ipv6网关不能为空
basic.param.ipv6.gateway.invalid=必须是有效的IPv6网关

# 上位机IPv4配置
basic.param.hostIpv4s.notNull=上位机ipv4配置数组不能为空
basic.param.hostIpv4s.size=上位机ipv4配置数组有效范围是[0,4]

# 上位机IPv6配置
basic.param.hostIpv6s.notNull=上位机ipv6配置数组不能为空
basic.param.hostIpv6s.size=上位机ipv6配置数组有效范围是[0,4]

# 上位机IP地址信息
basic.param.hostIp.enabled.notNull=是否启用不能为空
basic.param.hostIp.enabled.range=是否启用的有效范围是[0,1]
basic.param.hostIp.ip.notNull=地址不能为空
basic.param.hostIp.ip.invalid=必须是有效的IP地址
basic.param.hostIp.port.notNull=端口不能为空
basic.param.hostIp.port.range=端口的有效范围是(0,65535]
basic.param.hostIp.commType.notNull=通信类型不能为空
basic.param.hostIp.commType.range=通信类型的有效范围是[1,3]
basic.param.hostIp.protoType.notNull=协议类型不能为空
basic.param.hostIp.protoType.range=协议类型的有效范围是[1,5]

# 灯检测阈值配置
basic.param.lampFaultThre.lampGroupNo.notNull=灯组编号不能为空
basic.param.lampFaultThre.lampGroupNo.range=灯组编号有效范围[1,64]
basic.param.lampFaultThre.greenThreshold.notNull=绿灯电压电流阈值配置不能为空
basic.param.lampFaultThre.yellowThreshold.notNull=黄灯电压电流阈值配置不能为空
basic.param.lampFaultThre.redThreshold.notNull=红灯电压电流阈值配置不能为空

# 灯组信息
basic.param.lampGroup.lampGroupNo.notNull=灯组编号不能为空
basic.param.lampGroup.lampGroupNo.range=灯组编号有效范围[1,64]
basic.param.lampGroup.crossingSeqNo.notNull=子路口号不能为空
basic.param.lampGroup.crossingSeqNo.range=子路口号有效范围[0,8]
basic.param.lampGroup.detectFlag.notNull=检测标记不能为空
basic.param.lampGroup.detectFlag.range=检测标记有效范围[0,1]
basic.param.lampGroup.type.notNull=灯组类型不能为空
basic.param.lampGroup.direction.notNull=方向不能为空
basic.param.lampGroup.name.notNull=灯组名称不能为空

# 灯组参数信息
basic.param.lampGroupParam.lampGroups.notNull=灯组信息列表不能为空
basic.param.lampGroupParam.lampGroups.size=灯组信息列表范围[1,64]
basic.param.lampGroupParam.uiThreshold.notNull=灯检测电压电流阈值配置不能为空
basic.param.lampGroupParam.lampFaultThres.notNull=灯检测阈值配置不能为空

# 相位灯序参数
basic.param.lampSequence.lampSequenceNo.notNull=相位灯序编号不能为空
basic.param.lampSequence.lampSequenceNo.range=相位灯序编号有效范围为[1,64]
basic.param.lampSequence.sequenceType.notNull=相位灯序类型不能为空
basic.param.lampSequence.sequenceType.range=相位灯序类型有效范围为[0,3]
basic.param.lampSequence.sequenceName.notNull=相位灯序名称不能为空
basic.param.lampSequence.partTimeColor.notNull=待机灯色不能为空
basic.param.lampSequence.loseLightTransitionStartups.notNull=开机失去路权配置不能为空
basic.param.lampSequence.loseLightTransitionStartups.size=开机失去路权配置数量必须为4
basic.param.lampSequence.obtainLightTransitionStartups.notNull=开机获得路权配置不能为空
basic.param.lampSequence.obtainLightTransitionStartups.size=开机获得路权配置数量必须为4
basic.param.lampSequence.loseLightTransitionYellowFlashs.notNull=正常到待机失去路权配置不能为空
basic.param.lampSequence.loseLightTransitionYellowFlashs.size=正常到待机失去路权配置数量必须为4
basic.param.lampSequence.obtainLightTransitionYellowFlashs.notNull=正常到待机获得路权配置不能为空
basic.param.lampSequence.obtainLightTransitionYellowFlashs.size=正常到待机获得路权配置数量必须为4
basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.notNull=待机到正常失去路权配置不能为空
basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.size=待机到正常失去路权配置数量必须为4
basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.notNull=待机到正常获得路权配置不能为空
basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.size=待机到正常获得路权配置数量必须为4

# 逻辑输入信息
basic.param.logicInput.logicInputNo.notNull=逻辑输入编号不能为空
basic.param.logicInput.logicInputNo.min=逻辑输入编号不能小于1
basic.param.logicInput.logicInputNo.max=逻辑输入编号不能大于64
basic.param.logicInput.logicInputName.notNull=逻辑输入名称不能为空
basic.param.logicInput.installLocation.notNull=安装位置不能为空
basic.param.logicInput.state.notNull=状态信号不能为空
basic.param.logicInput.state.min=状态信号不能小于0
basic.param.logicInput.state.max=状态信号不能大于2
basic.param.logicInput.hold.notNull=需求记忆不能为空
basic.param.logicInput.hold.min=需求记忆不能小于0
basic.param.logicInput.hold.max=需求记忆不能大于1
basic.param.logicInput.duration.notNull=持续时长不能为空
basic.param.logicInput.duration.min=持续时长不能小于0
basic.param.logicInput.duration.max=持续时长不能大于65535
basic.param.logicInput.invert.notNull=取反不能为空
basic.param.logicInput.invert.min=取反不能小于0
basic.param.logicInput.invert.max=取反不能大于1
basic.param.logicInput.pairedDetector.notNull=配对检测器不能为空
basic.param.logicInput.pairedDetector.min=配对检测器不能小于0
basic.param.logicInput.pairedDetector.max=配对检测器不能大于64
basic.param.logicInput.pairedDetectorSpacing.notNull=配对检测器距离不能为空
basic.param.logicInput.pairedDetectorSpacing.min=配对检测器距离不能小于0
basic.param.logicInput.pairedDetectorSpacing.max=配对检测器距离不能大于65535
basic.param.logicInput.ped.notNull=行人检测不能为空
basic.param.logicInput.ped.min=行人检测不能小于0
basic.param.logicInput.ped.max=行人检测不能大于1
basic.param.logicInput.volume.notNull=流量检测不能为空
basic.param.logicInput.volume.min=流量检测不能小于0
basic.param.logicInput.volume.max=流量检测不能大于1
basic.param.logicInput.occupancy.notNull=占有率检测不能为空
basic.param.logicInput.occupancy.min=占有率检测不能小于0
basic.param.logicInput.occupancy.max=占有率检测不能大于1
basic.param.logicInput.speed.notNull=速度检测不能为空
basic.param.logicInput.speed.min=速度检测不能小于0
basic.param.logicInput.speed.max=速度检测不能大于1
basic.param.logicInput.queue.notNull=排队检测不能为空
basic.param.logicInput.queue.min=排队检测不能小于0
basic.param.logicInput.queue.max=排队检测不能大于1
basic.param.logicInput.count.notNull=数量检测不能为空
basic.param.logicInput.count.min=数量检测不能小于0
basic.param.logicInput.count.max=数量检测不能大于1
basic.param.logicInput.identity.notNull=身份检测不能为空
basic.param.logicInput.identity.min=身份检测不能小于0
basic.param.logicInput.identity.max=身份检测不能大于1
basic.param.logicInput.maxPresence.notNull=有效时间不能为空
basic.param.logicInput.maxPresence.min=有效时间不能小于0
basic.param.logicInput.maxPresence.max=有效时间不能大于65535
basic.param.logicInput.noActivity.notNull=无效时间不能为空
basic.param.logicInput.noActivity.min=无效时间不能小于0
basic.param.logicInput.noActivity.max=无效时间不能大于65535
basic.param.logicInput.extend.notNull=延长时间不能为空
basic.param.logicInput.extend.min=延长时间不能小于0
basic.param.logicInput.extend.max=延长时间不能大于255
basic.param.logicInput.delay.notNull=延迟时间不能为空
basic.param.logicInput.delay.min=延迟时间不能小于0
basic.param.logicInput.delay.max=延迟时间不能大于65535
basic.param.logicInput.failOperation.notNull=故障动作不能为空
basic.param.logicInput.failOperation.min=故障动作不能小于0
basic.param.logicInput.failOperation.max=故障动作不能大于1

# 方案阶段参数
basic.param.planStage.stageNo.notNull=阶段编号不能为空
basic.param.planStage.stageNo.min=阶段编号不能小于1
basic.param.planStage.stageNo.max=阶段编号不能大于64
basic.param.planStage.stageTime.notNull=阶段时长不能为空
basic.param.planStage.stageTime.min=阶段时长不能小于0
basic.param.planStage.stageTime.max=阶段时长不能大于65535
basic.param.planStage.stageActivationType.notNull=阶段出现类型不能为空
basic.param.planStage.stageActivationType.min=阶段出现类型不能小于1
basic.param.planStage.stageActivationType.max=阶段出现类型不能大于3
basic.param.planStage.coordinatedForceOff.notNull=协调强制关闭不能为空
basic.param.planStage.coordinatedForceOff.min=协调强制关闭不能小于1
basic.param.planStage.coordinatedForceOff.max=协调强制关闭不能大于2
basic.param.planStage.planStagePhaseParams.notNull=相位运行早截断参数不能为空
basic.param.planStage.planStagePhaseParams.size=相位运行早截断参数个数应在0到64之间

# 相位运行早截断参数
basic.param.planStagePhase.phaseNo.notNull=相位编号不能为空
basic.param.planStagePhase.phaseNo.min=相位编号不能小于1
basic.param.planStagePhase.phaseNo.max=相位编号不能大于64
basic.param.planStagePhase.laggingTime.notNull=迟启时长不能为空
basic.param.planStagePhase.laggingTime.min=迟启时长不能小于0
basic.param.planStagePhase.laggingTime.max=迟启时长不能大于255
basic.param.planStagePhase.delayCutOffTime.notNull=晚截时长不能为空
basic.param.planStagePhase.delayCutOffTime.min=晚截时长不能小于0
basic.param.planStagePhase.delayCutOffTime.max=晚截时长不能大于255

# 阶段相位参数信息
basic.param.stageParam.stageNo.notNull=阶段编号不能为空
basic.param.stageParam.stageNo.min=阶段编号不能小于1
basic.param.stageParam.stageNo.max=阶段编号不能大于64
basic.param.stageParam.stageName.notNull=阶段名称不能为空
basic.param.stageParam.demandsInsertLeavingManualAndFixStage.notNull=结束手动运行期时插入阶段需求不能为空
basic.param.stageParam.demandsInsertLeavingManualAndFixStage.discrete=结束手动运行期时插入阶段需求只能为0或1
basic.param.stageParam.demandsInsertStartUpStage.notNull=启动时插入阶段需求不能为空
basic.param.stageParam.demandsInsertStartUpStage.discrete=启动时插入阶段需求只能为0或1
basic.param.stageParam.windowsTime.notNull=窗口时间不能为空
basic.param.stageParam.windowsTime.min=窗口时间不能小于0
basic.param.stageParam.windowsTime.max=窗口时间不能大于255
basic.param.stageParam.stagePhaseParams.notNull=相位信息不能为空
basic.param.stageParam.stagePhaseParams.size=相位信息长度应在0到64之间
# StageParamInfo validation messages
basic.param.stageParam.demandsInsertLeavingHurryCall.notNull=紧急调用结束后插入需求不能为空
basic.param.stageParam.demandsInsertLeavingHurryCall.range=紧急调用结束后插入需求有效范围为[0,1]
basic.param.stageParam.demandsInsertLeavingSystem.notNull=中心控制结束后插入需求不能为空
basic.param.stageParam.demandsInsertLeavingSystem.range=中心控制结束后插入需求有效范围为[0,1]
basic.param.stageParam.unconditionalDemand.notNull=无条件默认需求不能为空
basic.param.stageParam.unconditionalDemand.range=无条件默认需求有效范围为[0,1] 

# 阶段相位参数
basic.param.stagePhase.phaseNo.notNull=相位编号不能为空
basic.param.stagePhase.phaseNo.min=相位编号不能小于1
basic.param.stagePhase.phaseNo.max=相位编号不能大于64
basic.param.stagePhase.demand.notNull=相位出现类型不能为空
basic.param.stagePhase.demand.discrete=相位出现类型只能为1或2
basic.param.stagePhase.laggingTime.notNull=迟启时长不能为空
basic.param.stagePhase.laggingTime.min=迟启时长不能小于0
basic.param.stagePhase.laggingTime.max=迟启时长不能大于255
basic.param.stagePhase.delayCutOffTime.notNull=晚截时长不能为空
basic.param.stagePhase.delayCutOffTime.min=晚截时长不能小于0
basic.param.stagePhase.delayCutOffTime.max=晚截时长不能大于255

# 阶段转移表
basic.param.stageTransTable.stageTransTableNo.notNull=阶段转移表编号不能为空
basic.param.stageTransTable.stageTransTableNo.range=阶段转移表编号有效范围[1,4]
basic.param.stageTransTable.stageTransConstraint.notNull=阶段转移参数不能为空
basic.param.stageTransTable.stageTransConstraint.size=阶段转移参数个数应为[1,64]

# 启动阶段参数
basic.param.startupStage.crossingSeqNo.notNull=子路口号不能为空
basic.param.startupStage.crossingSeqNo.min=子路口号不能小于1
basic.param.startupStage.crossingSeqNo.max=子路口号不能大于8
basic.param.startupStage.noDemandStage.notNull=无需求进入阶段不能为空
basic.param.startupStage.noDemandStage.min=无需求进入阶段不能小于0
basic.param.startupStage.noDemandStage.max=无需求进入阶段不能大于64
basic.param.startupStage.startupStage.notNull=启动进入阶段不能为空
basic.param.startupStage.startupStage.min=启动进入阶段不能小0
basic.param.startupStage.startupStage.max=启动进入阶段不能大于64

# 相位参数
basic.param.actuatedPhase.phaseNo.notNull=相位编号不能为空
basic.param.actuatedPhase.phaseNo.min=相位编号不能小于1
basic.param.actuatedPhase.phaseNo.max=相位编号不能大于64
basic.param.actuatedPhase.phasePassage.notNull=相位通过时间参数不能为空
basic.param.actuatedPhase.phasePassage.min=相位通过时间参数不能小于0
basic.param.actuatedPhase.phasePassage.max=相位通过时间参数不能大于255
basic.param.actuatedPhase.phaseAddedInitial.notNull=相位可变初始绿参数不能为空
basic.param.actuatedPhase.phaseAddedInitial.min=相位可变初始绿参数不能小于0
basic.param.actuatedPhase.phaseAddedInitial.max=相位可变初始绿参数不能大于255
basic.param.actuatedPhase.phaseMaximumInitial.notNull=相位最大初始绿参数不能为空
basic.param.actuatedPhase.phaseMaximumInitial.min=相位最大初始绿参数不能小于0
basic.param.actuatedPhase.phaseMaximumInitial.max=相位最大初始绿参数不能大于255
basic.param.actuatedPhase.phaseTimeBeforeReduction.notNull=相位通过时间调整前时间参数不能为空
basic.param.actuatedPhase.phaseTimeBeforeReduction.min=相位通过时间调整前时间参数不能小于0
basic.param.actuatedPhase.phaseTimeBeforeReduction.max=相位通过时间调整前时间参数不能大于255
basic.param.actuatedPhase.phaseTimeToReduce.notNull=相位通过时间调整时相位通过时间参数不能为空
basic.param.actuatedPhase.phaseTimeToReduce.min=相位通过时间调整时相位通过时间参数不能小于0
basic.param.actuatedPhase.phaseTimeToReduce.max=相位通过时间调整时相位通过时间参数不能大于255
basic.param.actuatedPhase.phaseReduceBy.notNull=相位车辆间隔参数不能为空
basic.param.actuatedPhase.phaseReduceBy.min=相位车辆间隔参数不能小于0
basic.param.actuatedPhase.phaseReduceBy.max=相位车辆间隔参数不能大于255
basic.param.actuatedPhase.phaseMinimumGap.notNull=相位车辆最小间隔参数不能为空
basic.param.actuatedPhase.phaseMinimumGap.min=相位车辆最小间隔参数不能小于0
basic.param.actuatedPhase.phaseMinimumGap.max=相位车辆最小间隔参数不能大于255
basic.param.actuatedPhase.phaseDynamicMaxLimit.notNull=相位动态最大值不能为空
basic.param.actuatedPhase.phaseDynamicMaxLimit.min=相位动态最大值不能小于0
basic.param.actuatedPhase.phaseDynamicMaxLimit.max=相位动态最大值不能大于255
basic.param.actuatedPhase.phaseDynamicMaxStep.notNull=相位动态最大值步长不能为空
basic.param.actuatedPhase.phaseDynamicMaxStep.min=相位动态最大值步长不能小于0
basic.param.actuatedPhase.phaseDynamicMaxStep.max=相位动态最大值步长不能大于255

# 条件参数
basic.param.conditioning.conditionings.notNull=条件运算列表不能为空
basic.param.conditioning.conditionings.size=条件运算列表个数应在1到64之间
basic.param.conditioning.actions.notNull=动作列表不能为空
basic.param.conditioning.actions.size=动作列表个数应在1到64之间

# 控制器参数
basic.param.controller.deviceVersion.notNull=设备版本不能为空
basic.param.controller.controlBoardSerialNumber.notNull=主控板序列号不能为空
basic.param.controller.lampDriverBoardMaxNumber.notNull=灯驱板数不能为空
basic.param.controller.lampDriverBoardMaxNumber.min=灯驱板最小个数为1
basic.param.controller.lampDriverBoardMaxNumber.max=灯驱板最大个数为16
basic.param.controller.lampDriverBoards.notNull=灯驱板信息不能为空
basic.param.controller.lampDriverBoards.size=灯驱板信息个数应该为16
basic.param.controller.detectionBoardMaxNumber.notNull=检测板数不能为空
basic.param.controller.detectionBoardMaxNumber.min=检测板最小个数为0
basic.param.controller.detectionBoardMaxNumber.max=检测板最大个数为4
basic.param.controller.detectionBoards.notNull=检测板信息不能为空
basic.param.controller.detectionBoards.size=检测板信息个数应该为4
basic.param.controller.informationBoardMaxNumber.notNull=信息板数不能为空
basic.param.controller.informationBoardMaxNumber.min=信息板最小个数为0
basic.param.controller.informationBoardMaxNumber.max=信息板最大个数为1
basic.param.controller.informationBoards.notNull=信息板信息不能为空
basic.param.controller.informationBoards.size=信息板信息个数应该为1
basic.param.controller.systemCordRef.notNull=系统协调零点不能为空
basic.param.controller.systemCordRef.range=系统协调零点有效范围[0,1440)
basic.param.controller.maxGreenWatchDog.notNull=看门狗最大绿不能为空
basic.param.controller.maxGreenWatchDog.range=看门狗最大绿有效范围[0,255]
basic.param.controller.startInterGreen.notNull=启动绿灯间隔不能为空
basic.param.controller.startInterGreen.range=启动绿灯间隔有效范围[0,32]
basic.param.controller.switchedSigns.notNull=可变标记个数不能为空
basic.param.controller.switchedSigns.range=可变标记有效范围[0,32]
basic.param.controller.stageSkipProhibited.notNull=阶段跳转禁止不能为空
basic.param.controller.stageSkipProhibited.range=阶段跳转禁止有效范围[0,1]
basic.param.controller.lampFaultDetect.notNull=故障响应不能为空
basic.param.controller.constraint.notNull=约束信息不能为空
basic.param.controller.modePriority.notNull=模式优先级不能为空
basic.param.controller.modePriority.size=模式优先级数组长度应该为255
basic.param.controller.lampGroupNumber.digitsInSet=灯组数必须是4、6或8
basic.param.controller.usedPhaseNum.notNull=使用相位数不能为空
basic.param.controller.usedPhaseNum.range=使用相位数有效范围是[2,64]
basic.param.controller.usedStageNum.notNull=使用阶段数不能为空
basic.param.controller.usedStageNum.range=使用阶段数有效范围是[1,64]
basic.param.controller.degradeAction.notNull=降级动作不能为空
basic.param.controller.degradeAction.range=降级动作有效范围是[0,1]
basic.param.controller.hurryCallExecuteWatchDog.notNull=紧急调用执行看门狗不能为空
basic.param.controller.hurryCallExecuteWatchDog.range=紧急调用执行看门狗有效范围是[0,255]
basic.param.controller.yellowAsWaitIndicator.notNull=黄灯作为等待指示器不能为空
basic.param.controller.yellowAsWaitIndicator.range=黄灯作为等待指示器有效范围是[0,1]
basic.param.controller.maxManualControlTime.notNull=最大手动控制时间不能为空
basic.param.controller.maxManualControlTime.range=最大手动控制时间有效范围[0,255]
basic.param.controller.flashOnTime.notNull=闪灯亮灯时长不能为空
basic.param.controller.flashOnTime.range=闪灯亮灯时长有效范围[400,1000]
basic.param.controller.flashOffTime.notNull=闪灯灭灯时长不能为空
basic.param.controller.flashOffTime.range=闪灯灭灯时长有效范围[400,1000]

# 倒计时参数
basic.param.countDown.countDown.notNull=倒计时配置不能为空
basic.param.countDown.countdownDisplayParams.notNull=倒计时显示参数不能为空
basic.param.countDown.countdownDisplayParams.size=倒计时显示参数个数应在1到32之间

# 日计划参数
basic.param.dayPlan.dayPlanNo.notNull=日计划编号不能为空
basic.param.dayPlan.dayPlanNo.min=日计划编号不能小于1
basic.param.dayPlan.dayPlanNo.max=日计划编号不能大于128
basic.param.dayPlan.crossingSeqNo.notNull=子路口号不能为空
basic.param.dayPlan.crossingSeqNo.min=子路口号不能小于1
basic.param.dayPlan.crossingSeqNo.max=子路口号不能大于8
basic.param.dayPlan.segmentParams.notNull=时段参数不能为空
basic.param.dayPlan.segmentParams.size=时段参数个数应在1到48之间
basic.param.dayPlan.dayPlanName.notNull=日计划名称不能为空
basic.param.dayPlan.segmentParams.startTime.order=时段开始时间必须按升序排列

# 紧急参数
basic.param.emergency.emergencies.notNull=紧急列表不能为空
basic.param.emergency.emergencies.size=紧急列表个数应在1到64之间
basic.param.emergency.manualPanels.notNull=手控控制列表不能为空
basic.param.emergency.manualPanels.size=手控控制列表个数应该是8

# 相位冲突参数
basic.param.incompatiblePhase.incompatiblePhases.notNull=相位冲突表不能为空
basic.param.incompatiblePhase.incompatiblePhases.size=相位冲突表个数应为[1,64]

# 绿间隔参数
basic.param.interGreenTime.interGreenTimeTables.notNull=绿间隔表不能为空
basic.param.interGreenTime.interGreenTimeTables.size=绿间隔表个数应为4
basic.param.interGreenTime.modeTrans.notNull=模式绿间隔表不能为空
basic.param.interGreenTime.modeTrans.size=模式绿间隔表个数应为255
basic.param.interGreenTime.modeTrans.discreteValuesList=模式绿间隔表有效范围[1,4]

# 逻辑参数
basic.param.logic.logicInputs.notNull=逻辑输入不能为空
basic.param.logic.logicInputs.size=逻辑输入个数应在1到64之间
basic.param.logic.logicOutputs.notNull=逻辑输出不能为空
basic.param.logic.logicOutputs.size=逻辑输出个数应在1到64之间
basic.param.logic.timers.notNull=定时器不能为空
basic.param.logic.timers.size=定时器个数应在1到64之间
basic.param.logic.userFlags.notNull=用户标志列表不能为空
basic.param.logic.userFlags.size=用户标志列表个数应在1到64之间

# 相位需求扩展参数
basic.param.phaseDemandExtend.phaseNo.notNull=相位编号不能为空
basic.param.phaseDemandExtend.phaseNo.min=相位编号不能小于1
basic.param.phaseDemandExtend.phaseNo.max=相位编号不能大于64
basic.param.phaseDemandExtend.demands.notNull=需求列表不能为空
basic.param.phaseDemandExtend.demands.size=需求列表个数应为8
basic.param.phaseDemandExtend.extensions.notNull=延长列表不能为空
basic.param.phaseDemandExtend.extensions.size=延长列表个数应为8
basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.notNull=结束手动运行期时插入相位需求不能为空
basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.range=结束手动运行期时插入相位需求有效范围为[0,1]
basic.param.phaseDemandExtend.demandsInsertStartUpPhase.notNull=启动时插入相位需求不能为空
basic.param.phaseDemandExtend.demandsInsertStartUpPhase.range=启动时插入相位需求有效范围为[0,1]
basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.notNull=紧急调用结束后插入需求不能为空
basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.range=紧急调用结束后插入需求有效范围为[0,1]
basic.param.phaseDemandExtend.demandsInsertLeavingSystem.notNull=中心控制结末后插入需求不能为空
basic.param.phaseDemandExtend.demandsInsertLeavingSystem.range=中心控制结末后插入需求有效范围为[0,1]
basic.param.phaseDemandExtend.unconditionalDemand.notNull=无条件默认需求不能为空
basic.param.phaseDemandExtend.unconditionalDemand.range=无条件默认需求有效范围为[0,1]
basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.notNull=非锁定需求最大相位最大绿不能为空
basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.range=非锁定需求最大相位最大绿有效范围为[0,1]
basic.param.phaseDemandExtend.minGreenDemand.notNull=最小绿需求不能为空
basic.param.phaseDemandExtend.minGreenDemand.range=最小绿需求有效范围为[0,1]
basic.param.phaseDemandExtend.maxGreenDemand.notNull=最大绿需求不能为空
basic.param.phaseDemandExtend.maxGreenDemand.range=最大绿需求有效范围为[0,1]
basic.param.phaseDemandExtend.revertivePhaseDemand.notNull=相位最大绿跟随相位需求不能为空
basic.param.phaseDemandExtend.revertivePhaseDemand.range=相位最大绿跟随相位需求有效范围为[0,64]

# 相位参数
basic.param.phase.phaseParams.notNull=相位参数列表不能为空
basic.param.phase.phaseParams.size=相位参数列表个数应该为64

# 相位参数信息
basic.param.phaseInfo.phaseNo.notNull=相位编号不能为空
basic.param.phaseInfo.phaseNo.range=相位编号有效范围是[1,64]
basic.param.phaseInfo.lampSequenceNo.notNull=相位灯序编号不能为空
basic.param.phaseInfo.lampSequenceNo.range=相位灯序编号有效范围是[0,64]
basic.param.phaseInfo.lampSequenceNo.range2=相位灯序编号使用有效范围是[1,64]
basic.param.phaseInfo.phaseLightsGroups.notNull=相位灯组不能为空
basic.param.phaseInfo.phaseLightsGroups.size=相位灯组长度有效范围是[64,64]
basic.param.phaseInfo.phaseLightsGroups.range=相位灯组有效范围是[0,1]
basic.param.phaseInfo.appearance.notNull=绿灯开始方式不能为空
basic.param.phaseInfo.appearance.range=绿灯开始方式有效范围是[0,2]
basic.param.phaseInfo.termination.notNull=绿灯结束方式不能为空
basic.param.phaseInfo.assocPhase.notNull=关联相位不能为空
basic.param.phaseInfo.assocPhase.range=关联相位有效范围是[0,64]
basic.param.phaseInfo.minGreenTime.notNull=相位最小绿时间1不能为空
basic.param.phaseInfo.minGreenTime.range=相位最小绿时间1有效范围为[0,30]
basic.param.phaseInfo.maxGreenTime1.notNull=相位最大绿时间1不能为空
basic.param.phaseInfo.maxGreenTime1.range=相位最大绿时间1有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime2.notNull=相位最大绿时间2不能为空
basic.param.phaseInfo.maxGreenTime2.range=相位最大绿时间2有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime3.notNull=相位最大绿时间3不能为空
basic.param.phaseInfo.maxGreenTime3.range=相位最大绿时间3有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime4.notNull=相位最大绿时间4不能为空
basic.param.phaseInfo.maxGreenTime4.range=相位最大绿时间4有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime5.notNull=相位最大绿时间5不能为空
basic.param.phaseInfo.maxGreenTime5.range=相位最大绿时间5有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime6.notNull=相位最大绿时间6不能为空
basic.param.phaseInfo.maxGreenTime6.range=相位最大绿时间6有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime7.notNull=相位最大绿时间7不能为空
basic.param.phaseInfo.maxGreenTime7.range=相位最大绿时间7有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime8.notNull=相位最大绿时间8不能为空
basic.param.phaseInfo.maxGreenTime8.range=相位最大绿时间8有效范围为[0,255]
basic.param.phaseInfo.maxGreenTime1.greaterThanMinGreen=最大绿灯时间1必须大于最小绿灯时间
basic.param.phaseInfo.maxGreenTime.sequential=最大绿灯时间必须从maxGreenTime1到maxGreenTime8依次递增

# 方案参数
basic.param.plan.planNo.notNull=方案编号不能为空
basic.param.plan.planNo.min=方案编号不能小于1
basic.param.plan.planNo.max=方案编号不能大于128
basic.param.plan.crossingSeqNo.notNull=子路口号不能为空
basic.param.plan.crossingSeqNo.min=子路口号不能小于1
basic.param.plan.crossingSeqNo.max=子路口号不能大于8
basic.param.plan.phaseTableNo.notNull=相位表不能为空
basic.param.plan.phaseTableNo.min=相位表不能小于1
basic.param.plan.phaseTableNo.max=相位表不能大于10
basic.param.plan.cycle.notNull=周期不能为空
basic.param.plan.cycle.min=周期不能小于0
basic.param.plan.cycle.max=周期不能大于65535
basic.param.plan.coordinatedStageSeq.notNull=协调序号不能为空
basic.param.plan.coordinatedStageSeq.min=协调序号不能小于0
basic.param.plan.coordinatedStageSeq.max=协调序号不能大于16
basic.param.plan.coordinatedRef.notNull=协调参考点不能为空
basic.param.plan.coordinatedRef.min=协调参考点不能小于0
basic.param.plan.coordinatedRef.max=协调参考点不能大于2
basic.param.plan.offset.notNull=相位差不能为空
basic.param.plan.offset.min=相位差不能小于0
basic.param.plan.offset.max=相位差不能大于65535
basic.param.plan.planStageParams.notNull=方案阶段参数不能为空
basic.param.plan.planStageParams.size=方案阶段参数个数应在2到16之间
basic.param.plan.planName.notNull=方案名称不能为空

# 调度参数
basic.param.schedule.scheduleNo.notNull=调度表编号不能为空
basic.param.schedule.scheduleNo.min=调度表编号不能小于1
basic.param.schedule.scheduleNo.max=调度表编号不能大于128
basic.param.schedule.crossingSeqNo.notNull=子路口号不能为空
basic.param.schedule.crossingSeqNo.min=子路口号不能小于1
basic.param.schedule.crossingSeqNo.max=子路口号不能大于8
basic.param.schedule.priority.notNull=优先级不能为空
basic.param.schedule.priority.min=优先级不能小于0
basic.param.schedule.priority.max=优先级不能大于255
basic.param.schedule.week.notNull=星期值不能为空
basic.param.schedule.month.notNull=月份不能为空
basic.param.schedule.day.notNull=日期不能为空
basic.param.schedule.dayPlanNo.notNull=日计划号不能为空
basic.param.schedule.dayPlanNo.min=日计划号不能小于1
basic.param.schedule.dayPlanNo.max=日计划号不能大于128
basic.param.schedule.scheduleName.notNull=调度计划名称不能为空

# 阶段参数
basic.param.stage.stageParams.notNull=阶段相位参数列表不能为空
basic.param.stage.stageParams.size=阶段相位参数列表参数个数应在1到64之间
basic.param.stage.startupStages.notNull=子路口启动参数列表不能为空
basic.param.stage.startupStages.size=子路口启动参数列表应在1到8之间

# 阶段转换参数
basic.param.stageTransition.stageTransTables.notNull=阶段转移表不能为空
basic.param.stageTransition.stageTransTables.size=阶段转移表个数应为4
basic.param.stageTransition.modeTrans.notNull=模式阶段转移表不能为空
basic.param.stageTransition.modeTrans.size=模式阶段转移表个数应为255
basic.param.stageTransition.modeTrans.discreteValuesList=模式阶段转移表允许范围[1,4]

# 条件计算参数
juncer.param.conditioning.conditioningNo.notNull=条件计算编号不能为空
juncer.param.conditioning.conditioningNo.range=条件计算编号有效范围为[1,64]
juncer.param.conditioning.outputType.notNull=结果类型不能为空
juncer.param.conditioning.outputNo.notNull=结果编号不能为空
juncer.param.conditioning.op1.notNull=操作符1不能为空
juncer.param.conditioning.paramType1.notNull=变量类型1不能为空
juncer.param.conditioning.paramNo1.notNull=变量编号1不能为空
juncer.param.conditioning.op2.notNull=操作符2不能为空
juncer.param.conditioning.paramType2.notNull=变量类型2不能为空
juncer.param.conditioning.paramNo2.notNull=变量编号2不能为空
juncer.param.conditioning.op3.notNull=操作符3不能为空
juncer.param.conditioning.paramType3.notNull=变量类型3不能为空
juncer.param.conditioning.paramNo3.notNull=变量编号3不能为空
juncer.param.conditioning.op4.notNull=操作符4不能为空
juncer.param.conditioning.paramType4.notNull=变量类型4不能为空
juncer.param.conditioning.paramNo4.notNull=变量编号4不能为空
juncer.param.conditioning.conditioningName.notNull=条件计算名称不能为空

# 条件动作参数
juncer.param.action.actionNo.notNull=条件动作编号不能为空
juncer.param.action.actionNo.range=条件动作编号有效范围为[1,64]
juncer.param.action.paramType.notNull=变量类型不能为空
juncer.param.action.paramNo.notNull=变量编号不能为空
juncer.param.action.functionNo.notNull=功能编号不能为空
juncer.param.action.param1.notNull=参数1不能为空
juncer.param.action.param2.notNull=参数2不能为空
juncer.param.action.actionName.notNull=动作名称不能为空

# 灯控板参数
juncer.param.lampDriverBoard.enabled.range=是否启用范围是[0,1]
juncer.param.lampDriverBoard.serialNumber.notNull=灯控板序列号不能为空

# 检测板参数
juncer.param.detectionBoard.enabled.range=是否启用范围是[0,1]
juncer.param.detectionBoard.serialNumber.notNull=检测板序列号不能为空

# 信息板参数
juncer.param.informationBoard.enabled.range=是否启用范围是[0,1]
juncer.param.informationBoard.serialNumber.notNull=信息板序列号不能为空


# LampFaultDetect validation messages
juncer.param.lampFaultDetect.detectCycle.notNull=检测周期不能为空
juncer.param.lampFaultDetect.detectCycle.range=检测周期有效范围是[1,3]
juncer.param.lampFaultDetect.redFaultDetection.notNull=红灯损坏检测降级上报配置不能为空
juncer.param.lampFaultDetect.redFaultDetection.range=红灯损坏检测降级上报配置有效范围是[0,4]
juncer.param.lampFaultDetect.greenConflictDetection.notNull=绿冲突检测降级上报配置不能为空
juncer.param.lampFaultDetect.greenConflictDetection.range=绿冲突检测降级上报配置有效范围是[0,4]
juncer.param.lampFaultDetect.redGreenDetection.notNull=红绿同亮检测降级上报配置不能为空
juncer.param.lampFaultDetect.redGreenDetection.range=红绿同亮检测降级上报配置有效范围是[0,4]
juncer.param.lampFaultDetect.yellowFaultDetection.notNull=黄灯损坏检测降级上报配置不能为空
juncer.param.lampFaultDetect.yellowFaultDetection.range=黄灯损坏检测降级上报配置有效范围是[0,4]
juncer.param.lampFaultDetect.greenFaultDetection.notNull=绿灯损坏检测降级上报配置不能为空
juncer.param.lampFaultDetect.greenFaultDetection.range=绿灯损坏检测降级上报配置有效范围是[0,4]
juncer.param.lampFaultDetect.redYellowDetection.notNull=红黄同亮检测降级上报配置不能为空
juncer.param.lampFaultDetect.redYellowDetection.range=红黄同亮检测降级上报配置有效范围是[0,4]
juncer.param.lampFaultDetect.yellowGreenDetection.notNull=黄绿同亮检测降级上报配置不能为空
juncer.param.lampFaultDetect.yellowGreenDetection.range=黄绿同亮检测降级上报配置有效范围是[0,4]

# Constraint validation messages
juncer.param.constraint.minGreenConstraintLower.notNull=最小绿最小约束不能为空
juncer.param.constraint.minGreenConstraintLower.range=最小绿最小约束有效范围[0,30]
juncer.param.constraint.minGreenConstraintUpper.notNull=最小绿最大约束不能为空
juncer.param.constraint.minGreenConstraintUpper.range=最小绿最大约束有效范围[0,30]
juncer.param.constraint.maxGreenConstraintLower.notNull=最大绿最小约束不能为空
juncer.param.constraint.maxGreenConstraintLower.range=最大绿最小约束有效范围[0,255]
juncer.param.constraint.maxGreenConstraintUpper.notNull=最大绿最大约束不能为空
juncer.param.constraint.maxGreenConstraintUpper.range=最大绿最大约束有效范围[0,255]
juncer.param.constraint.pedFlashingGreenLower.notNull=行人绿闪最小约束不能为空
juncer.param.constraint.pedFlashingGreenLower.range=行人绿闪最小约束有效范围[0,32]
juncer.param.constraint.pedFlashingGreenUpper.notNull=行人绿闪最大约束不能为空
juncer.param.constraint.pedFlashingGreenUpper.range=行人绿闪最大约束有效范围[0,32]
juncer.param.constraint.interGreenLower.notNull=绿间隔最小约束不能为空
juncer.param.constraint.interGreenLower.range=绿间隔最小约束有效范围[0,30]
juncer.param.constraint.interGreenUpper.notNull=绿间隔最大约束不能为空
juncer.param.constraint.interGreenUpper.range=绿间隔最大约束有效范围[0,30]
juncer.param.constraint.extGreenLower.notNull=绿延长最小约束不能为空
juncer.param.constraint.extGreenLower.range=绿延长最小约束有效范围[0,150]
juncer.param.constraint.extGreenUpper.notNull=绿延长最大约束不能为空
juncer.param.constraint.extGreenUpper.range=绿延长最大约束有效范围[0,150]
juncer.param.constraint.minGreenConstraint.valid=最小绿最小约束应小于最小绿最大约束
juncer.param.constraint.maxGreenConstraint.valid=最大绿最小约束应小于最大绿最大约束
juncer.param.constraint.pedFlashingGreenConstraint.valid=行人绿闪最小约束应小于行人绿闪最大约束
juncer.param.constraint.interGreenConstraint.valid=绿间隔最小约束应小于绿间隔最大约束
juncer.param.constraint.extGreenConstraint.valid=绿延长最小约束应小于绿延长最大约束

# CountdownInfo validation messages
juncer.param.countdownInfo.enableType.notNull=倒计时启用类型不能为空
juncer.param.countdownInfo.enableType.range=倒计时启用类型有效范围[0,3]
juncer.param.countdownInfo.baudRate.notNull=通信波特率不能为空
juncer.param.countdownInfo.baudRate.range=通信波特率有效范围[0,3]
juncer.param.countdownInfo.parity.notNull=校验位不能为空
juncer.param.countdownInfo.parity.range=校验位有效范围[0,2]
juncer.param.countdownInfo.pulseBrightWidth.notNull=脉冲亮屏宽度不能为空
juncer.param.countdownInfo.pulseBrightWidth.range=脉冲亮屏宽度有效范围[0,5]
juncer.param.countdownInfo.pulseBrightColor.notNull=脉冲亮屏灯色不能为空
juncer.param.countdownInfo.pulseBrightColor.range=脉冲亮屏灯色有效范围[0,1]
juncer.param.countdownInfo.pulseDarkWidth.notNull=脉冲灭屏宽度不能为空
juncer.param.countdownInfo.pulseDarkWidth.range=脉冲灭屏宽度有效范围[0,5]
juncer.param.countdownInfo.pulseDarkColor.notNull=脉冲灭屏灯色不能为空
juncer.param.countdownInfo.pulseDarkColor.range=脉冲灭屏灯色有效范围[0,2]
juncer.param.countdownInfo.redVehicleFlag.notNull=触发红灯倒计时机动车标志不能为空
juncer.param.countdownInfo.redVehicleFlag.range=触发红灯倒计时机动车标志有效范围[0,1]
juncer.param.countdownInfo.redVehicleTime.notNull=触发红灯倒计时机动车剩余时间不能为空
juncer.param.countdownInfo.redVehicleTime.range=触发红灯倒计时机动车剩余时间有效范围[1,20]
juncer.param.countdownInfo.greenVehicleFlag.notNull=触发绿灯倒计时机动车标志不能为空
juncer.param.countdownInfo.greenVehicleFlag.range=触发绿灯倒计时机动车标志有效范围[0,1]
juncer.param.countdownInfo.greenVehicleTime.notNull=触发绿灯倒计时机动车剩余时间不能为空
juncer.param.countdownInfo.greenVehicleTime.range=触发绿灯倒计时机动车剩余时间有效范围[1,20]
juncer.param.countdownInfo.redPedestrianFlag.notNull=触发红灯倒计时行人标志不能为空
juncer.param.countdownInfo.redPedestrianFlag.range=触发红灯倒计时行人标志有效范围[0,1]
juncer.param.countdownInfo.redPedestrianTime.notNull=触发红灯倒计时行人剩余时间不能为空
juncer.param.countdownInfo.redPedestrianTime.range=触发红灯倒计时行人剩余时间有效范围[1,20]
juncer.param.countdownInfo.greenPedestrianFlag.notNull=触发绿灯倒计时行人标志不能为空
juncer.param.countdownInfo.greenPedestrianFlag.range=触发绿灯倒计时行人标志有效范围[0,1]
juncer.param.countdownInfo.greenPedestrianTime.notNull=触发绿灯倒计时行人剩余时间不能为空
juncer.param.countdownInfo.greenPedestrianTime.range=触发绿灯倒计时行人剩余时间有效范围[1,20]
juncer.param.countdownInfo.inductionFlag.notNull=感应模式触发倒计时标志不能为空
juncer.param.countdownInfo.inductionFlag.range=感应模式触发倒计时标志有效范围[0,1]

# CountdownDisplayParam validation messages
juncer.param.countdownDisplayParam.countdownDisplayNo.notNull=倒计时屏编号不能为空
juncer.param.countdownDisplayParam.countdownDisplayNo.range=倒计时屏编号有效范围[1,32]
juncer.param.countdownDisplayParam.phase1.notNull=相位1不能为空
juncer.param.countdownDisplayParam.phase1.range=相位号1有效范围[0,64]
juncer.param.countdownDisplayParam.phase2.notNull=相位2不能为空
juncer.param.countdownDisplayParam.phase2.range=相位号2有效范围[0,64]

# SegmentAction validation messages
juncer.param.segmentAction.functionNo.notNull=功能编号不能为空
juncer.param.segmentAction.param1.notNull=参数1不能为空
juncer.param.segmentAction.param2.notNull=参数2不能为空

# SegmentParam validation messages
juncer.param.segmentParam.startTime.notNull=开始时间不能为空
juncer.param.segmentParam.startTime.range=开始时间有效范围为[0,1439]
juncer.param.segmentParam.planNo.notNull=方案号不能为空
juncer.param.segmentParam.planNo.range=方案号有效范围为[1,128]
juncer.param.segmentParam.controlMode.notNull=控制方式不能为空
juncer.param.segmentParam.controlMode.range=控制方式有效范围为[0,255]
juncer.param.segmentParam.coordCorrectionMode.notNull=方案过渡方式不能为空
juncer.param.segmentParam.coordCorrectionMode.range=方案过渡方式有效范围为[0,3]
juncer.param.segmentParam.coordDirection.notNull=协调方向不能为空
juncer.param.segmentParam.coordDirection.range=协调方向有效范围为[0,255]
juncer.param.segmentParam.actionNum.notNull=动作链个数不能为空
juncer.param.segmentParam.actionNum.range=动作链个数有效范围为[0,16]
juncer.param.segmentParam.segmentActions.notNull=动作链不能为空
juncer.param.segmentParam.segmentActions.size=动作链数量有效范围为[0,16]

# Emergency validation messages
juncer.param.emergency.emergencyNo.notNull=紧急编号不能为空
juncer.param.emergency.emergencyNo.range=紧急编号有效范围为[1,64]
juncer.param.emergency.stageNo.notNull=紧急申请相位阶段不能为空
juncer.param.emergency.stageNo.range=紧急申请相位阶段有效范围为[1,64]
juncer.param.emergency.priority.notNull=紧急申请优先级不能为空
juncer.param.emergency.priority.range=紧急申请优先级有效范围为[0,255]
juncer.param.emergency.delayTime.notNull=延迟时间不能为空
juncer.param.emergency.delayTime.range=延迟时间有效范围为[0,255]
juncer.param.emergency.durationTime.notNull=持续时间不能为空
juncer.param.emergency.durationTime.range=持续时间有效范围为[0,255]
juncer.param.emergency.callIntervalTime.notNull=呼叫间隔时间不能为空
juncer.param.emergency.callIntervalTime.range=呼叫间隔时间有效范围为[0,255]
juncer.param.emergency.paramTypeCall.notNull=需求变量类型不能为空
juncer.param.emergency.paramNoCall.notNull=需求变量编号不能为空
juncer.param.emergency.paramTypeCancel.notNull=删除变量类型不能为空
juncer.param.emergency.paramNoCancel.notNull=删除变量编号不能为空
juncer.param.emergency.paramTypeConfirm.notNull=确认变量类型不能为空
juncer.param.emergency.paramNoConfirm.notNull=确认变量编号不能为空
juncer.param.emergency.callWatchDog.notNull=紧急优先呼叫看门狗不能为空
juncer.param.emergency.callWatchDog.range=紧急优先呼叫看门狗有效范围为[0,255]

# ManualPanel validation messages
juncer.param.manualPanel.buttonNo.notNull=手控按钮编号不能为空
juncer.param.manualPanel.buttonNo.range=手控按钮编号有效范围为[1,8]
juncer.param.manualPanel.callStageNo.notNull=呼叫阶段编号不能为空
juncer.param.manualPanel.callStageNo.size=呼叫阶段编号数量必须为8
juncer.param.manualPanel.callStageNo.range=呼叫阶段编号有效范围为[0,64]

# IncompatiblePhase validation messages
juncer.param.incompatiblePhase.phaseNo.notNull=相位编号不能为空
juncer.param.incompatiblePhase.phaseNo.range=相位编号有效范围是[1,64]
juncer.param.incompatiblePhase.incompatiblePhaseSeq.notNull=冲突对立相位序列不能为空
juncer.param.incompatiblePhase.incompatiblePhaseSeq.size=冲突对立相位序列长度必须为[1,64]
juncer.param.incompatiblePhase.incompatiblePhaseSeq.range=冲突对立相位序列有效范围是[0,3]

# IncompatiblePhases validation messages
juncer.param.incompatiblePhases.incompatiblePhases.notNull=相位冲突配置表列表不能为空
juncer.param.incompatiblePhases.incompatiblePhases.size=相位冲突配置表列表长度必须为[1,64]

# InterGreenTime validation messages
juncer.param.interGreenTime.phaseNo.notNull=相位编号不能为空
juncer.param.interGreenTime.phaseNo.range=相位编号有效范围是[1,64]
juncer.param.interGreenTime.interGreenTimeSeq.notNull=绿间隔时间序列不能为空
juncer.param.interGreenTime.interGreenTimeSeq.size=绿间隔时间序列长度必须为64
juncer.param.interGreenTime.interGreenTimeSeq.range=绿间隔时间序列有效范围是[0,30]

# InterGreenTimeTable validation messages
juncer.param.interGreenTimeTable.interGreenTimeTableNo.notNull=相位绿间隔表号不能为空
juncer.param.interGreenTimeTable.interGreenTimeTableNo.range=相位绿间隔表号有效范围是[1,4]
juncer.param.interGreenTimeTable.interGreenTimes.notNull=相位绿间隔配置列表不能为空
juncer.param.interGreenTimeTable.interGreenTimes.size=相位绿间隔配置列表长度必须为64

# UIThreshold validation messages
juncer.param.uiThreshold.voltageThresholdUpper.notNull=电压阈值上限不能为空
juncer.param.uiThreshold.voltageThresholdUpper.range=电压阈值上限有效范围是[0,255]
juncer.param.uiThreshold.voltageThresholdLower.notNull=电压阈值下限不能为空
juncer.param.uiThreshold.voltageThresholdLower.range=电压阈值下限有效范围是[0,255]
juncer.param.uiThreshold.currentThresholdUpper.notNull=电流阈值上限不能为空
juncer.param.uiThreshold.currentThresholdUpper.range=电流阈值上限有效范围是[0,255]
juncer.param.uiThreshold.currentThresholdLower.notNull=电流阈值下限不能为空
juncer.param.uiThreshold.currentThresholdLower.range=电流阈值下限有效范围是[0,255]
juncer.param.uiThreshold.voltageThreshold.valid=电压阈值上限必须大于电压阈值下限
juncer.param.uiThreshold.currentThreshold.valid=电流阈值上限必须大于电流阈值下限

# Threshold validation messages
juncer.param.threshold.voltageUpper.notNull=电压阈值上限不能为空
juncer.param.threshold.voltageLower.notNull=电压阈值下限不能为空
juncer.param.threshold.currentUpper.notNull=电流阈值上限不能为空
juncer.param.threshold.currentLower.notNull=电流阈值下限不能为空
juncer.param.threshold.voltage.upperGreaterThanLower=电压上限必须大于电压下限
juncer.param.threshold.current.upperGreaterThanLower=电流上限必须大于电流下限
juncer.param.threshold.voltageUpper.range=电压阈值上限有效范围是[0,510]
juncer.param.threshold.voltageLower.range=电压阈值下限有效范围是[0,510]
juncer.param.threshold.currentUpper.range=电流阈值上限有效范围是[0,3000]
juncer.param.threshold.currentLower.range=电流阈值下限有效范围是[0,3000]

# LightTransition validation messages
juncer.param.lightTransition.colorType.notNull=灯色类型不能为空
juncer.param.lightTransition.colorType.range=灯色类型有效范围为[0,14]
juncer.param.lightTransition.colorTime.notNull=灯色时长不能为空
juncer.param.lightTransition.colorTime.range=灯色时长有效范围为[0,99]

# UserFlag validation messages
juncer.param.userFlag.userFlagNo.notNull=用户变量编号不能为空
juncer.param.userFlag.userFlagNo.range=用户变量编号有效范围为[1,64]
juncer.param.userFlag.userFlagName.notNull=用户变量名称不能为空

# Timer validation messages
juncer.param.timer.timerNo.notNull=定时器编号不能为空
juncer.param.timer.timerNo.range=定时器编号有效范围为[1,64]
juncer.param.timer.timerName.notNull=定时器名称不能为空
juncer.param.timer.interval.notNull=时间间隔不能为空
juncer.param.timer.interval.range=时间间隔有效范围为[0,65535]

# LogicOutput validation messages
juncer.param.logicOutput.logicOutputNo.notNull=逻辑输出编号不能为空
juncer.param.logicOutput.logicOutputNo.range=逻辑输出编号有效范围为[1,64]
juncer.param.logicOutput.logicOutputName.notNull=逻辑输出名称不能为空

# Demand validation messages
juncer.param.demand.inputType.notNull=需求类型不能为空
juncer.param.demand.inputNo.notNull=需求编号不能为空
juncer.param.demand.inputFlag.notNull=参数标记不能为空

# Extension validation messages
juncer.param.extension.inputType.notNull=需求类型不能为空
juncer.param.extension.inputNo.notNull=需求编号不能为空
juncer.param.extension.inputFlag.notNull=参数标记不能为空

# StageTransitionConstraint validation messages
juncer.param.stageTransitionConstraint.stageNo.notNull=相位阶段编号不能为空
juncer.param.stageTransitionConstraint.stageNo.range=相位阶段编号有效范围是[1,64]
juncer.param.stageTransitionConstraint.transitionConstraints.notNull=相位阶段过渡约束值不能为空
juncer.param.stageTransitionConstraint.transitionConstraints.size=相位阶段过渡约束值长度必须为64
juncer.param.stageTransitionConstraint.transitionConstraints.range=相位阶段过渡约束值有效范围是[0,64]及255
