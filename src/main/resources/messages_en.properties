# Basic parameter validation messages
# Area number
basic.param.noArea.notNull=Area number cannot be empty
basic.param.noArea.range=Area number must be between [0,39]

# Junction number
basic.param.noJunc.notNull=Junction number cannot be empty
basic.param.noJunc.range=Junction number must be between [0,499]

# Signal machine type
basic.param.type.notNull=Signal machine type cannot be empty

# Production date
basic.param.productionDate.notNull=Production date cannot be empty

# Longitude
basic.param.lon.notNull=Longitude cannot be empty

# Latitude
basic.param.lat.notNull=Latitude cannot be empty

basic.param.cmmType.match=Communication type does not match IP information 

# Controlled junction number
basic.param.controlledJunctionNum.notNull=Controlled junction number cannot be empty
basic.param.controlledJunctionNum.range=Controlled junction number must be between [1,8]

# Installation intersection
basic.param.installIntersection.notNull=Installation intersection cannot be empty

# Communication mode
basic.param.ipEnabled.notNull=Communication mode cannot be empty
basic.param.ipEnabled.range=Communication mode must be between [0,2]

# MAC address
basic.param.macAddress.notNull=Signal machine MAC address cannot be empty
basic.param.macAddress.invalid=Invalid MAC address

# IPv4 information
basic.param.ipv4.notNull=Signal machine IPv4 information cannot be empty
basic.param.ipv4.ip.notNull=IPv4 address cannot be empty
basic.param.ipv4.ip.invalid=Must be a valid IPv4 address
basic.param.ipv4.mask.notNull=Subnet mask cannot be empty
basic.param.ipv4.mask.invalid=Must be a valid subnet mask
basic.param.ipv4.gateway.notNull=Gateway cannot be empty
basic.param.ipv4.gateway.invalid=Must be a valid gateway

# IPv6 information
basic.param.ipv6.notNull=Signal machine IPv6 information cannot be empty
basic.param.ipv6.ip.notNull=IPv6 address cannot be empty
basic.param.ipv6.ip.invalid=Must be a valid IPv6 address
basic.param.ipv6.mask.notNull=IPv6 subnet mask cannot be empty
basic.param.ipv6.mask.invalid=Must be a valid IPv6 subnet mask
basic.param.ipv6.gateway.notNull=IPv6 gateway cannot be empty
basic.param.ipv6.gateway.invalid=Must be a valid IPv6 gateway

# Host IPv4 configuration
basic.param.hostIpv4s.notNull=Host IPv4 configuration array cannot be empty
basic.param.hostIpv4s.size=Host IPv4 configuration array must be between [0,4]

# Host IPv6 configuration
basic.param.hostIpv6s.notNull=Host IPv6 configuration array cannot be empty
basic.param.hostIpv6s.size=Host IPv6 configuration array must be between [0,4]

# Host IP address information
basic.param.hostIp.enabled.notNull=Enable status cannot be empty
basic.param.hostIp.enabled.range=Enable status must be between [0,1]
basic.param.hostIp.ip.notNull=Address cannot be empty
basic.param.hostIp.ip.invalid=Must be a valid IP address
basic.param.hostIp.port.notNull=Port cannot be empty
basic.param.hostIp.port.range=Port must be between (0,65535]
basic.param.hostIp.commType.notNull=Communication type cannot be empty
basic.param.hostIp.commType.range=Communication type must be between [1,3]
basic.param.hostIp.protoType.notNull=Protocol type cannot be empty
basic.param.hostIp.protoType.range=Protocol type must be between [1,5]

# Lamp fault threshold configuration
basic.param.lampFaultThre.lampGroupNo.notNull=Lamp group number cannot be empty
basic.param.lampFaultThre.lampGroupNo.range=Lamp group number must be between [1,64]
basic.param.lampFaultThre.greenThreshold.notNull=Green light voltage current threshold configuration cannot be empty
basic.param.lampFaultThre.yellowThreshold.notNull=Yellow light voltage current threshold configuration cannot be empty
basic.param.lampFaultThre.redThreshold.notNull=Red light voltage current threshold configuration cannot be empty

# Lamp group information
basic.param.lampGroup.lampGroupNo.notNull=Lamp group number cannot be empty
basic.param.lampGroup.lampGroupNo.range=Lamp group number must be between [1,64]
basic.param.lampGroup.crossingSeqNo.notNull=Crossing sequence number cannot be empty
basic.param.lampGroup.crossingSeqNo.range=Crossing sequence number must be between [0,8]
basic.param.lampGroup.detectFlag.notNull=Detection flag cannot be empty
basic.param.lampGroup.detectFlag.range=Detection flag must be between [0,1]
basic.param.lampGroup.type.notNull=Lamp group type cannot be empty
basic.param.lampGroup.direction.notNull=Direction cannot be empty
basic.param.lampGroup.name.notNull=Lamp group name cannot be empty

# Lamp group parameter information
basic.param.lampGroupParam.lampGroups.notNull=Lamp group information list cannot be empty
basic.param.lampGroupParam.lampGroups.size=Lamp group information list must be between [1,64]
basic.param.lampGroupParam.uiThreshold.notNull=Lamp detection voltage current threshold configuration cannot be empty
basic.param.lampGroupParam.lampFaultThres.notNull=Lamp detection threshold configuration cannot be empty

# Phase lamp sequence parameters
basic.param.lampSequence.lampSequenceNo.notNull=Phase lamp sequence number cannot be empty
basic.param.lampSequence.lampSequenceNo.range=Phase lamp sequence number must be between [1,64]
basic.param.lampSequence.sequenceType.notNull=Phase lamp sequence type cannot be empty
basic.param.lampSequence.sequenceType.range=Phase lamp sequence type must be between [0,3]
basic.param.lampSequence.sequenceName.notNull=Phase lamp sequence name cannot be empty
basic.param.lampSequence.partTimeColor.notNull=Part-time color cannot be empty
basic.param.lampSequence.loseLightTransitionStartups.notNull=Startup right-of-way loss configuration cannot be empty
basic.param.lampSequence.loseLightTransitionStartups.size=Startup right-of-way loss configuration must contain exactly 4 entries
basic.param.lampSequence.obtainLightTransitionStartups.notNull=Startup right-of-way obtain configuration cannot be empty
basic.param.lampSequence.obtainLightTransitionStartups.size=Startup right-of-way obtain configuration must contain exactly 4 entries
basic.param.lampSequence.loseLightTransitionYellowFlashs.notNull=Normal to part-time right-of-way loss configuration cannot be empty
basic.param.lampSequence.loseLightTransitionYellowFlashs.size=Normal to part-time right-of-way loss configuration must contain exactly 4 entries
basic.param.lampSequence.obtainLightTransitionYellowFlashs.notNull=Normal to part-time right-of-way obtain configuration cannot be empty
basic.param.lampSequence.obtainLightTransitionYellowFlashs.size=Normal to part-time right-of-way obtain configuration must contain exactly 4 entries
basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.notNull=Part-time to normal right-of-way loss configuration cannot be empty
basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.size=Part-time to normal right-of-way loss configuration must contain exactly 4 entries
basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.notNull=Part-time to normal right-of-way obtain configuration cannot be empty
basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.size=Part-time to normal right-of-way obtain configuration must contain exactly 4 entries

# Logic input information
basic.param.logicInput.logicInputNo.notNull=Logic input number cannot be empty
basic.param.logicInput.logicInputNo.min=Logic input number cannot be less than 1
basic.param.logicInput.logicInputNo.max=Logic input number cannot be greater than 64
basic.param.logicInput.logicInputName.notNull=Logic input name cannot be empty
basic.param.logicInput.installLocation.notNull=Installation location cannot be empty
basic.param.logicInput.state.notNull=State signal cannot be empty
basic.param.logicInput.state.min=State signal cannot be less than 0
basic.param.logicInput.state.max=State signal cannot be greater than 2
basic.param.logicInput.hold.notNull=Demand memory cannot be empty
basic.param.logicInput.hold.min=Demand memory cannot be less than 0
basic.param.logicInput.hold.max=Demand memory cannot be greater than 1
basic.param.logicInput.duration.notNull=Duration cannot be empty
basic.param.logicInput.duration.min=Duration cannot be less than 0
basic.param.logicInput.duration.max=Duration cannot be greater than 65535
basic.param.logicInput.invert.notNull=Invert cannot be empty
basic.param.logicInput.invert.min=Invert cannot be less than 0
basic.param.logicInput.invert.max=Invert cannot be greater than 1
basic.param.logicInput.pairedDetector.notNull=Paired detector cannot be empty
basic.param.logicInput.pairedDetector.min=Paired detector cannot be less than 0
basic.param.logicInput.pairedDetector.max=Paired detector cannot be greater than 64
basic.param.logicInput.pairedDetectorSpacing.notNull=Paired detector spacing cannot be empty
basic.param.logicInput.pairedDetectorSpacing.min=Paired detector spacing cannot be less than 0
basic.param.logicInput.pairedDetectorSpacing.max=Paired detector spacing cannot be greater than 65535
basic.param.logicInput.ped.notNull=Pedestrian detection cannot be empty
basic.param.logicInput.ped.min=Pedestrian detection cannot be less than 0
basic.param.logicInput.ped.max=Pedestrian detection cannot be greater than 1
basic.param.logicInput.volume.notNull=Volume detection cannot be empty
basic.param.logicInput.volume.min=Volume detection cannot be less than 0
basic.param.logicInput.volume.max=Volume detection cannot be greater than 1
basic.param.logicInput.occupancy.notNull=Occupancy detection cannot be empty
basic.param.logicInput.occupancy.min=Occupancy detection cannot be less than 0
basic.param.logicInput.occupancy.max=Occupancy detection cannot be greater than 1
basic.param.logicInput.speed.notNull=Speed detection cannot be empty
basic.param.logicInput.speed.min=Speed detection cannot be less than 0
basic.param.logicInput.speed.max=Speed detection cannot be greater than 1
basic.param.logicInput.queue.notNull=Queue detection cannot be empty
basic.param.logicInput.queue.min=Queue detection cannot be less than 0
basic.param.logicInput.queue.max=Queue detection cannot be greater than 1
basic.param.logicInput.count.notNull=Count detection cannot be empty
basic.param.logicInput.count.min=Count detection cannot be less than 0
basic.param.logicInput.count.max=Count detection cannot be greater than 1
basic.param.logicInput.identity.notNull=Identity detection cannot be empty
basic.param.logicInput.identity.min=Identity detection cannot be less than 0
basic.param.logicInput.identity.max=Identity detection cannot be greater than 1
basic.param.logicInput.maxPresence.notNull=Maximum presence time cannot be empty
basic.param.logicInput.maxPresence.min=Maximum presence time cannot be less than 0
basic.param.logicInput.maxPresence.max=Maximum presence time cannot be greater than 65535
basic.param.logicInput.noActivity.notNull=No activity time cannot be empty
basic.param.logicInput.noActivity.min=No activity time cannot be less than 0
basic.param.logicInput.noActivity.max=No activity time cannot be greater than 65535
basic.param.logicInput.extend.notNull=Extension time cannot be empty
basic.param.logicInput.extend.min=Extension time cannot be less than 0
basic.param.logicInput.extend.max=Extension time cannot be greater than 255
basic.param.logicInput.delay.notNull=Delay time cannot be empty
basic.param.logicInput.delay.min=Delay time cannot be less than 0
basic.param.logicInput.delay.max=Delay time cannot be greater than 65535
basic.param.logicInput.failOperation.notNull=Fail operation cannot be empty
basic.param.logicInput.failOperation.min=Fail operation cannot be less than 0
basic.param.logicInput.failOperation.max=Fail operation cannot be greater than 1

# Plan stage parameters
basic.param.planStage.stageNo.notNull=Stage number cannot be empty
basic.param.planStage.stageNo.min=Stage number cannot be less than 1
basic.param.planStage.stageNo.max=Stage number cannot be greater than 64
basic.param.planStage.stageTime.notNull=Stage duration cannot be empty
basic.param.planStage.stageTime.min=Stage duration cannot be less than 0
basic.param.planStage.stageTime.max=Stage duration cannot be greater than 65535
basic.param.planStage.stageActivationType.notNull=Stage activation type cannot be empty
basic.param.planStage.stageActivationType.min=Stage activation type cannot be less than 1
basic.param.planStage.stageActivationType.max=Stage activation type cannot be greater than 3
basic.param.planStage.coordinatedForceOff.notNull=Coordinated force off cannot be empty
basic.param.planStage.coordinatedForceOff.min=Coordinated force off cannot be less than 1
basic.param.planStage.coordinatedForceOff.max=Coordinated force off cannot be greater than 2
basic.param.planStage.planStagePhaseParams.notNull=Phase early truncation parameters cannot be empty
basic.param.planStage.planStagePhaseParams.size=Number of phase early truncation parameters must be between 0 and 64

# Phase early truncation parameters
basic.param.planStagePhase.phaseNo.notNull=Phase number cannot be empty
basic.param.planStagePhase.phaseNo.min=Phase number cannot be less than 1
basic.param.planStagePhase.phaseNo.max=Phase number cannot be greater than 64
basic.param.planStagePhase.laggingTime.notNull=Lagging time cannot be empty
basic.param.planStagePhase.laggingTime.min=Lagging time cannot be less than 0
basic.param.planStagePhase.laggingTime.max=Lagging time cannot be greater than 255
basic.param.planStagePhase.delayCutOffTime.notNull=Delay cut-off time cannot be empty
basic.param.planStagePhase.delayCutOffTime.min=Delay cut-off time cannot be less than 0
basic.param.planStagePhase.delayCutOffTime.max=Delay cut-off time cannot be greater than 255

# Stage phase parameter information
basic.param.stageParam.stageNo.notNull=Stage number cannot be empty
basic.param.stageParam.stageNo.min=Stage number cannot be less than 1
basic.param.stageParam.stageNo.max=Stage number cannot be greater than 64
basic.param.stageParam.stageName.notNull=Stage name cannot be empty
basic.param.stageParam.demandsInsertLeavingManualAndFixStage.notNull=Stage demand insertion when leaving manual operation cannot be empty
basic.param.stageParam.demandsInsertLeavingManualAndFixStage.discrete=Stage demand insertion when leaving manual operation must be either 0 or 1
basic.param.stageParam.demandsInsertStartUpStage.notNull=Stage demand insertion at startup cannot be empty
basic.param.stageParam.demandsInsertStartUpStage.discrete=Stage demand insertion at startup must be either 0 or 1
basic.param.stageParam.windowsTime.notNull=Window time cannot be empty
basic.param.stageParam.windowsTime.min=Window time cannot be less than 0
basic.param.stageParam.windowsTime.max=Window time cannot be greater than 255
basic.param.stageParam.stagePhaseParams.notNull=Phase information cannot be empty
basic.param.stageParam.stagePhaseParams.size=Number of phase information entries must be between 0 and 64

# Stage phase parameters
basic.param.stagePhase.phaseNo.notNull=Phase number cannot be empty
basic.param.stagePhase.phaseNo.min=Phase number cannot be less than 1
basic.param.stagePhase.phaseNo.max=Phase number cannot be greater than 64
basic.param.stagePhase.demand.notNull=Phase appearance type cannot be empty
basic.param.stagePhase.demand.discrete=Phase appearance type must be either 1 or 2
basic.param.stagePhase.laggingTime.notNull=Lagging time cannot be empty
basic.param.stagePhase.laggingTime.min=Lagging time cannot be less than 0
basic.param.stagePhase.laggingTime.max=Lagging time cannot be greater than 255
basic.param.stagePhase.delayCutOffTime.notNull=Delay cut-off time cannot be empty
basic.param.stagePhase.delayCutOffTime.min=Delay cut-off time cannot be less than 0
basic.param.stagePhase.delayCutOffTime.max=Delay cut-off time cannot be greater than 255

# Stage transition table
basic.param.stageTransTable.stageTransTableNo.notNull=Stage transition table number cannot be empty
basic.param.stageTransTable.stageTransTableNo.range=Stage transition table number must be between [1,4]
basic.param.stageTransTable.stageTransConstraint.notNull=Stage transition parameters cannot be empty
basic.param.stageTransTable.stageTransConstraint.size=Number of stage transition parameters must be [1,64]

# Startup stage parameters
basic.param.startupStage.crossingSeqNo.notNull=Crossing sequence number cannot be empty
basic.param.startupStage.crossingSeqNo.min=Crossing sequence number cannot be less than 1
basic.param.startupStage.crossingSeqNo.max=Crossing sequence number cannot be greater than 8
basic.param.startupStage.noDemandStage.notNull=No demand entry stage cannot be empty
basic.param.startupStage.noDemandStage.min=No demand entry stage cannot be less than 0
basic.param.startupStage.noDemandStage.max=No demand entry stage cannot be greater than 64
basic.param.startupStage.startupStage.notNull=Startup entry stage cannot be empty
basic.param.startupStage.startupStage.min=Startup entry stage cannot be less than 0
basic.param.startupStage.startupStage.max=Startup entry stage cannot be greater than 64

# Phase parameters
basic.param.actuatedPhase.phaseNo.notNull=Phase number cannot be empty
basic.param.actuatedPhase.phaseNo.min=Phase number cannot be less than 1
basic.param.actuatedPhase.phaseNo.max=Phase number cannot be greater than 64
basic.param.actuatedPhase.phasePassage.notNull=Phase passage time parameter cannot be empty
basic.param.actuatedPhase.phasePassage.min=Phase passage time parameter cannot be less than 0
basic.param.actuatedPhase.phasePassage.max=Phase passage time parameter cannot be greater than 255
basic.param.actuatedPhase.phaseAddedInitial.notNull=Phase variable initial green parameter cannot be empty
basic.param.actuatedPhase.phaseAddedInitial.min=Phase variable initial green parameter cannot be less than 0
basic.param.actuatedPhase.phaseAddedInitial.max=Phase variable initial green parameter cannot be greater than 255
basic.param.actuatedPhase.phaseMaximumInitial.notNull=Phase maximum initial green parameter cannot be empty
basic.param.actuatedPhase.phaseMaximumInitial.min=Phase maximum initial green parameter cannot be less than 0
basic.param.actuatedPhase.phaseMaximumInitial.max=Phase maximum initial green parameter cannot be greater than 255
basic.param.actuatedPhase.phaseTimeBeforeReduction.notNull=Phase time before reduction parameter cannot be empty
basic.param.actuatedPhase.phaseTimeBeforeReduction.min=Phase time before reduction parameter cannot be less than 0
basic.param.actuatedPhase.phaseTimeBeforeReduction.max=Phase time before reduction parameter cannot be greater than 255
basic.param.actuatedPhase.phaseTimeToReduce.notNull=Phase time to reduce parameter cannot be empty
basic.param.actuatedPhase.phaseTimeToReduce.min=Phase time to reduce parameter cannot be less than 0
basic.param.actuatedPhase.phaseTimeToReduce.max=Phase time to reduce parameter cannot be greater than 255
basic.param.actuatedPhase.phaseReduceBy.notNull=Phase vehicle interval parameter cannot be empty
basic.param.actuatedPhase.phaseReduceBy.min=Phase vehicle interval parameter cannot be less than 0
basic.param.actuatedPhase.phaseReduceBy.max=Phase vehicle interval parameter cannot be greater than 255
basic.param.actuatedPhase.phaseMinimumGap.notNull=Phase minimum vehicle gap parameter cannot be empty
basic.param.actuatedPhase.phaseMinimumGap.min=Phase minimum vehicle gap parameter cannot be less than 0
basic.param.actuatedPhase.phaseMinimumGap.max=Phase minimum vehicle gap parameter cannot be greater than 255
basic.param.actuatedPhase.phaseDynamicMaxLimit.notNull=Phase dynamic maximum value cannot be empty
basic.param.actuatedPhase.phaseDynamicMaxLimit.min=Phase dynamic maximum value cannot be less than 0
basic.param.actuatedPhase.phaseDynamicMaxLimit.max=Phase dynamic maximum value cannot be greater than 255
basic.param.actuatedPhase.phaseDynamicMaxStep.notNull=Phase dynamic maximum step size cannot be empty
basic.param.actuatedPhase.phaseDynamicMaxStep.min=Phase dynamic maximum step size cannot be less than 0
basic.param.actuatedPhase.phaseDynamicMaxStep.max=Phase dynamic maximum step size cannot be greater than 255

# Conditioning parameters
basic.param.conditioning.conditionings.notNull=Conditioning operations list cannot be empty
basic.param.conditioning.conditionings.size=Number of conditioning operations must be between 1 and 64
basic.param.conditioning.actions.notNull=Actions list cannot be empty
basic.param.conditioning.actions.size=Number of actions must be between 1 and 64

# Controller parameters
basic.param.controller.deviceVersion.notNull=Device version cannot be empty
basic.param.controller.controlBoardSerialNumber.notNull=Control board serial number cannot be empty
basic.param.controller.lampDriverBoardMaxNumber.notNull=Number of lamp driver boards cannot be empty
basic.param.controller.lampDriverBoardMaxNumber.min=Minimum number of lamp driver boards is 1
basic.param.controller.lampDriverBoardMaxNumber.max=Maximum number of lamp driver boards is 16
basic.param.controller.lampDriverBoards.notNull=Lamp driver board information cannot be empty
basic.param.controller.lampDriverBoards.size=Lamp driver board information entries must be exactly 16
basic.param.controller.detectionBoardMaxNumber.notNull=Number of detection boards cannot be empty
basic.param.controller.detectionBoardMaxNumber.min=Minimum number of detection boards is 0
basic.param.controller.detectionBoardMaxNumber.max=Maximum number of detection boards is 4
basic.param.controller.detectionBoards.notNull=Detection board information cannot be empty
basic.param.controller.detectionBoards.size=Number of detection board information entries must be exactly 4
basic.param.controller.informationBoardMaxNumber.notNull=Number of information boards cannot be empty
basic.param.controller.informationBoardMaxNumber.min=Minimum number of information boards is 0
basic.param.controller.informationBoardMaxNumber.max=Maximum number of information boards is 1
basic.param.controller.informationBoards.notNull=Information board information cannot be empty
basic.param.controller.informationBoards.size=Number of information board information entries must be exactly 1
basic.param.controller.systemCordRef.notNull=System coordination reference point cannot be empty
basic.param.controller.systemCordRef.range=System coordination reference point must be between [0,1440)
basic.param.controller.maxGreenWatchDog.notNull=Maximum green watchdog cannot be empty
basic.param.controller.maxGreenWatchDog.range=Maximum green watchdog must be between [0,255]
basic.param.controller.startInterGreen.notNull=Start inter-green interval cannot be empty
basic.param.controller.startInterGreen.range=Start inter-green interval must be between [0,32]
basic.param.controller.switchedSigns.notNull=Number of switched signs cannot be empty
basic.param.controller.switchedSigns.range=Number of switched signs must be between [0,32]
basic.param.controller.stageSkipProhibited.notNull=Stage skip prohibited cannot be empty
basic.param.controller.stageSkipProhibited.range=Stage skip prohibited must be between [0,1]
basic.param.controller.lampFaultDetect.notNull=Lamp fault detection cannot be empty
basic.param.controller.constraint.notNull=Constraint information cannot be empty
basic.param.controller.modePriority.notNull=Mode priority cannot be empty
basic.param.controller.modePriority.size=Mode priority array length should be 255
basic.param.controller.lampGroupNumber.digitsInSet=Lamp group number must be 4, 6, or 8
basic.param.controller.usedPhaseNum.notNull=Number of used phases cannot be null
basic.param.controller.usedPhaseNum.range=Number of used phases must be between [2,64]
basic.param.controller.usedStageNum.notNull=Number of used stages cannot be null
basic.param.controller.usedStageNum.range=Number of used stages must be between [1,64]
basic.param.controller.degradeAction.notNull=Degrade action cannot be null
basic.param.controller.degradeAction.range=Degrade action must be between [0,1]
basic.param.controller.hurryCallExecuteWatchDog.notNull=Hurry call execute watchdog cannot be null
basic.param.controller.hurryCallExecuteWatchDog.range=Hurry call execute watchdog must be between [0,255]
basic.param.controller.yellowAsWaitIndicator.notNull=Yellow as wait indicator cannot be null
basic.param.controller.yellowAsWaitIndicator.range=Yellow as wait indicator must be between [0,1]
basic.param.controller.maxManualControlTime.notNull=Maximum manual control time cannot be empty
basic.param.controller.maxManualControlTime.range=Maximum manual control time must be between [0,255]
basic.param.controller.flashOnTime.notNull=Flash on duration cannot be empty
basic.param.controller.flashOnTime.range=Flash on duration must be between [400,1000]
basic.param.controller.flashOffTime.notNull=Flash off duration cannot be empty
basic.param.controller.flashOffTime.range=Flash off duration must be between [400,1000]

# Countdown parameters
basic.param.countDown.countDown.notNull=Countdown configuration cannot be empty
basic.param.countDown.countdownDisplayParams.notNull=Countdown display parameters cannot be empty
basic.param.countDown.countdownDisplayParams.size=Number of countdown display parameters must be between 1 and 32

# Day plan parameters
basic.param.dayPlan.dayPlanNo.notNull=Day plan number cannot be empty
basic.param.dayPlan.dayPlanNo.min=Day plan number cannot be less than 1
basic.param.dayPlan.dayPlanNo.max=Day plan number cannot be greater than 128
basic.param.dayPlan.crossingSeqNo.notNull=Crossing sequence number cannot be empty
basic.param.dayPlan.crossingSeqNo.min=Crossing sequence number cannot be less than 1
basic.param.dayPlan.crossingSeqNo.max=Crossing sequence number cannot be greater than 8
basic.param.dayPlan.segmentParams.notNull=Segment parameters cannot be empty
basic.param.dayPlan.segmentParams.size=Number of segment parameters must be between 1 and 48
basic.param.dayPlan.dayPlanName.notNull=Day plan name cannot be empty
basic.param.dayPlan.segmentParams.startTime.order=Segment start times must be in ascending order

# Emergency parameters
basic.param.emergency.emergencies.notNull=Emergency list cannot be empty
basic.param.emergency.emergencies.size=Number of emergency entries must be between 1 and 64
basic.param.emergency.manualPanels.notNull=Manual control list cannot be empty
basic.param.emergency.manualPanels.size=Manual control list must contain exactly 8 entries

# Incompatible phase parameters
basic.param.incompatiblePhase.incompatiblePhases.notNull=Incompatible phase table cannot be empty
basic.param.incompatiblePhase.incompatiblePhases.size=Incompatible phase table must contain [1,64] entries

# Inter-green time parameters
basic.param.interGreenTime.interGreenTimeTables.notNull=Inter-green time table cannot be empty
basic.param.interGreenTime.interGreenTimeTables.size=Inter-green time table must contain exactly 4 entries
basic.param.interGreenTime.modeTrans.notNull=Mode inter-green time table cannot be empty
basic.param.interGreenTime.modeTrans.size=Mode inter-green time table must contain exactly 255 entries
basic.param.interGreenTime.modeTrans.discreteValuesList=Mode inter-green time value must be between 1 and 4
# Logic parameters
basic.param.logic.logicInputs.notNull=Logic inputs cannot be empty
basic.param.logic.logicInputs.size=Number of logic inputs must be between 1 and 64
basic.param.logic.logicOutputs.notNull=Logic outputs cannot be empty
basic.param.logic.logicOutputs.size=Number of logic outputs must be between 1 and 64
basic.param.logic.timers.notNull=Timers cannot be empty
basic.param.logic.timers.size=Number of timers must be between 1 and 64
basic.param.logic.userFlags.notNull=User flags list cannot be empty
basic.param.logic.userFlags.size=Number of user flags must be between 1 and 64

# Phase demand extension parameters
basic.param.phaseDemandExtend.phaseNo.notNull=Phase number cannot be empty
basic.param.phaseDemandExtend.phaseNo.min=Phase number cannot be less than 1
basic.param.phaseDemandExtend.phaseNo.max=Phase number cannot be greater than 64
basic.param.phaseDemandExtend.demands.notNull=Demand list cannot be empty
basic.param.phaseDemandExtend.demands.size=Demand list must contain exactly 8 entries
basic.param.phaseDemandExtend.extensions.notNull=Extension list cannot be empty
basic.param.phaseDemandExtend.extensions.size=Extension list must contain exactly 8 entries
basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.notNull=Phase demand insertion when leaving manual operation cannot be empty
basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.range=Phase demand insertion when leaving manual operation must be between [0,1]
basic.param.phaseDemandExtend.demandsInsertStartUpPhase.notNull=Phase demand insertion at startup cannot be empty
basic.param.phaseDemandExtend.demandsInsertStartUpPhase.range=Phase demand insertion at startup must be between [0,1]
basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.notNull=Demand insertion after emergency call cannot be empty
basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.range=Demand insertion after emergency call must be between [0,1]
basic.param.phaseDemandExtend.demandsInsertLeavingSystem.notNull=Demand insertion after center control cannot be empty
basic.param.phaseDemandExtend.demandsInsertLeavingSystem.range=Demand insertion after center control must be between [0,1]
basic.param.phaseDemandExtend.unconditionalDemand.notNull=Unconditional default demand cannot be empty
basic.param.phaseDemandExtend.unconditionalDemand.range=Unconditional default demand must be between [0,1]
basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.notNull=Unlatched demand start max green phase cannot be empty
basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.range=Unlatched demand start max green phase must be between [0,1]
basic.param.phaseDemandExtend.minGreenDemand.notNull=Minimum green demand cannot be empty
basic.param.phaseDemandExtend.minGreenDemand.range=Minimum green demand must be between [0,1]
basic.param.phaseDemandExtend.maxGreenDemand.notNull=Maximum green demand cannot be empty
basic.param.phaseDemandExtend.maxGreenDemand.range=Maximum green demand must be between [0,1]
basic.param.phaseDemandExtend.revertivePhaseDemand.notNull=Revertive phase demand cannot be empty
basic.param.phaseDemandExtend.revertivePhaseDemand.range=Revertive phase demand must be between [0,64]


# 相位参数
basic.param.phase.phaseParams.notNull=Phase List cannot be empty
basic.param.phase.phaseParams.size=Phase List must contain exactly 64 entries

# Phase parameter information
basic.param.phaseInfo.phaseNo.notNull=Phase number cannot be empty
basic.param.phaseInfo.phaseNo.range=Phase number must be between [1,64]
basic.param.phaseInfo.lampSequenceNo.notNull=Phase lamp sequence number cannot be empty
basic.param.phaseInfo.lampSequenceNo.range=Phase lamp sequence number must be between [0,64]
basic.param.phaseInfo.lampSequenceNo.range2=Phase lamp sequence number which is used must be between [1,64]
basic.param.phaseInfo.phaseLightsGroups.notNull=Phase light groups cannot be empty
basic.param.phaseInfo.phaseLightsGroups.size=Phase light groups must have exactly 64 entries
basic.param.phaseInfo.phaseLightsGroups.range=Phase light groups must be between [0,1]
basic.param.phaseInfo.appearance.notNull=Green start mode cannot be empty
basic.param.phaseInfo.appearance.range=Green start mode must be between [0,2]
basic.param.phaseInfo.termination.notNull=Green end mode cannot be empty
basic.param.phaseInfo.assocPhase.notNull=Associated phase cannot be empty
basic.param.phaseInfo.assocPhase.range=Associated phase must be between [0,64]
basic.param.phaseInfo.minGreenTime.notNull=Phase minimum green time 1 cannot be empty
basic.param.phaseInfo.minGreenTime.range=Phase minimum green time 1 must be between [0,30]
basic.param.phaseInfo.maxGreenTime1.notNull=Phase maximum green time 1 cannot be empty
basic.param.phaseInfo.maxGreenTime1.range=Phase maximum green time 1 must be between [0,255]
basic.param.phaseInfo.maxGreenTime2.notNull=Phase maximum green time 2 cannot be empty
basic.param.phaseInfo.maxGreenTime2.range=Phase maximum green time 2 must be between [0,255]
basic.param.phaseInfo.maxGreenTime3.notNull=Phase maximum green time 3 cannot be empty
basic.param.phaseInfo.maxGreenTime3.range=Phase maximum green time 3 must be between [0,255]
basic.param.phaseInfo.maxGreenTime4.notNull=Phase maximum green time 4 cannot be empty
basic.param.phaseInfo.maxGreenTime4.range=Phase maximum green time 4 must be between [0,255]
basic.param.phaseInfo.maxGreenTime5.notNull=Phase maximum green time 5 cannot be empty
basic.param.phaseInfo.maxGreenTime5.range=Phase maximum green time 5 must be between [0,255]
basic.param.phaseInfo.maxGreenTime6.notNull=Phase maximum green time 6 cannot be empty
basic.param.phaseInfo.maxGreenTime6.range=Phase maximum green time 6 must be between [0,255]
basic.param.phaseInfo.maxGreenTime7.notNull=Phase maximum green time 7 cannot be empty
basic.param.phaseInfo.maxGreenTime7.range=Phase maximum green time 7 must be between [0,255]
basic.param.phaseInfo.maxGreenTime8.notNull=Phase maximum green time 8 cannot be empty
basic.param.phaseInfo.maxGreenTime8.range=Phase maximum green time 8 must be between [0,255]
basic.param.phaseInfo.maxGreenTime1.greaterThanMinGreen=Max green time 1 must be greater than min green time
basic.param.phaseInfo.maxGreenTime.sequential=Max green times must increase sequentially from maxGreenTime1 to maxGreenTime8

# Plan parameters
basic.param.plan.planNo.notNull=Plan number cannot be empty
basic.param.plan.planNo.min=Plan number cannot be less than 1
basic.param.plan.planNo.max=Plan number cannot be greater than 128
basic.param.plan.crossingSeqNo.notNull=Crossing sequence number cannot be empty
basic.param.plan.crossingSeqNo.min=Crossing sequence number cannot be less than 1
basic.param.plan.crossingSeqNo.max=Crossing sequence number cannot be greater than 8
basic.param.plan.phaseTableNo.notNull=Phase table cannot be empty
basic.param.plan.phaseTableNo.min=Phase table cannot be less than 1
basic.param.plan.phaseTableNo.max=Phase table cannot be greater than 10
basic.param.plan.cycle.notNull=Cycle cannot be empty
basic.param.plan.cycle.min=Cycle cannot be less than 0
basic.param.plan.cycle.max=Cycle cannot be greater than 65535
basic.param.plan.coordinatedStageSeq.notNull=Coordinated stage sequence cannot be empty
basic.param.plan.coordinatedStageSeq.min=Coordinated stage sequence cannot be less than 0
basic.param.plan.coordinatedStageSeq.max=Coordinated stage sequence cannot be greater than 16
basic.param.plan.coordinatedRef.notNull=Coordinated reference point cannot be empty
basic.param.plan.coordinatedRef.min=Coordinated reference point cannot be less than 0
basic.param.plan.coordinatedRef.max=Coordinated reference point cannot be greater than 2
basic.param.plan.offset.notNull=Offset cannot be empty
basic.param.plan.offset.min=Offset cannot be less than 0
basic.param.plan.offset.max=Offset cannot be greater than 65535
basic.param.plan.planStageParams.notNull=Plan stage parameters cannot be empty
basic.param.plan.planStageParams.size=Number of plan stage parameters must be between 2 and 16
basic.param.plan.planName.notNull=Plan name cannot be empty

# Schedule parameters
basic.param.schedule.scheduleNo.notNull=Schedule table number cannot be empty
basic.param.schedule.scheduleNo.min=Schedule table number cannot be less than 1
basic.param.schedule.scheduleNo.max=Schedule table number cannot be greater than 128
basic.param.schedule.crossingSeqNo.notNull=Crossing sequence number cannot be empty
basic.param.schedule.crossingSeqNo.min=Crossing sequence number cannot be less than 1
basic.param.schedule.crossingSeqNo.max=Crossing sequence number cannot be greater than 8
basic.param.schedule.priority.notNull=Priority cannot be empty
basic.param.schedule.priority.min=Priority cannot be less than 0
basic.param.schedule.priority.max=Priority cannot be greater than 255
basic.param.schedule.week.notNull=Week value cannot be empty
basic.param.schedule.month.notNull=Month cannot be empty
basic.param.schedule.day.notNull=Day cannot be empty
basic.param.schedule.dayPlanNo.notNull=Day plan number cannot be empty
basic.param.schedule.dayPlanNo.min=Day plan number cannot be less than 1
basic.param.schedule.dayPlanNo.max=Day plan number cannot be greater than 128
basic.param.schedule.scheduleName.notNull=Schedule plan name cannot be empty

# Stage parameters
basic.param.stage.stageParams.notNull=Stage phase parameter list cannot be empty
basic.param.stage.stageParams.size=Stage phase parameter list elements must be between 1 and 64
basic.param.stage.startupStages.notNull=Crossing startup parameter list cannot be empty
basic.param.stage.startupStages.size=Crossing startup parameter list elements must be between 1 and 8

# Stage transition parameters
basic.param.stageTransition.stageTransTables.notNull=Stage transition table cannot be empty
basic.param.stageTransition.stageTransTables.size=Number of stage transition tables must be 4
basic.param.stageTransition.modeTrans.notNull=Mode stage transition table cannot be empty
basic.param.stageTransition.modeTrans.size=Mode stage transition table must contain exactly 255 elements
basic.param.stageTransition.modeTrans.discreteValuesList=Mode stage transition table value must be between [1,4]

# Conditioning parameters
juncer.param.conditioning.conditioningNo.notNull=Conditioning number cannot be empty
juncer.param.conditioning.conditioningNo.range=Conditioning number must be between [1,64]
juncer.param.conditioning.outputType.notNull=Output type cannot be empty
juncer.param.conditioning.outputNo.notNull=Output number cannot be empty
juncer.param.conditioning.op1.notNull=Operator 1 cannot be empty
juncer.param.conditioning.paramType1.notNull=Parameter type 1 cannot be empty
juncer.param.conditioning.paramNo1.notNull=Parameter number 1 cannot be empty
juncer.param.conditioning.op2.notNull=Operator 2 cannot be empty
juncer.param.conditioning.paramType2.notNull=Parameter type 2 cannot be empty
juncer.param.conditioning.paramNo2.notNull=Parameter number 2 cannot be empty
juncer.param.conditioning.op3.notNull=Operator 3 cannot be empty
juncer.param.conditioning.paramType3.notNull=Parameter type 3 cannot be empty
juncer.param.conditioning.paramNo3.notNull=Parameter number 3 cannot be empty
juncer.param.conditioning.op4.notNull=Operator 4 cannot be empty
juncer.param.conditioning.paramType4.notNull=Parameter type 4 cannot be empty
juncer.param.conditioning.paramNo4.notNull=Parameter number 4 cannot be empty
juncer.param.conditioning.conditioningName.notNull=Conditioning name cannot be empty

# Action parameters
juncer.param.action.actionNo.notNull=Action number cannot be empty
juncer.param.action.actionNo.range=Action number must be between [1,64]
juncer.param.action.paramType.notNull=Parameter type cannot be empty
juncer.param.action.paramNo.notNull=Parameter number cannot be empty
juncer.param.action.functionNo.notNull=Function number cannot be empty
juncer.param.action.param1.notNull=Parameter 1 cannot be empty
juncer.param.action.param2.notNull=Parameter 2 cannot be empty
juncer.param.action.actionName.notNull=Action name cannot be empty

# Lamp driver board parameters
juncer.param.lampDriverBoard.enabled.range=Enable status must be between [0,1]
juncer.param.lampDriverBoard.serialNumber.notNull=Lamp driver board serial number cannot be empty

# Detection board parameters
juncer.param.detectionBoard.enabled.range=Enable status must be between [0,1]
juncer.param.detectionBoard.serialNumber.notNull=Detection board serial number cannot be empty

# Information board parameters
juncer.param.informationBoard.enabled.range=Enable status must be between [0,1]
juncer.param.informationBoard.serialNumber.notNull=Information board serial number cannot be empty 



# LampFaultDetect validation messages
juncer.param.lampFaultDetect.detectCycle.notNull=Detection cycle cannot be empty
juncer.param.lampFaultDetect.detectCycle.range=Detection cycle valid range is [1,3]
juncer.param.lampFaultDetect.redFaultDetection.notNull=Red light fault detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.redFaultDetection.range=Red light fault detection downgrade report configuration valid range is [0,4]
juncer.param.lampFaultDetect.greenConflictDetection.notNull=Green conflict detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.greenConflictDetection.range=Green conflict detection downgrade report configuration valid range is [0,4]
juncer.param.lampFaultDetect.redGreenDetection.notNull=Red-green simultaneous detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.redGreenDetection.range=Red-green simultaneous detection downgrade report configuration valid range is [0,4]
juncer.param.lampFaultDetect.yellowFaultDetection.notNull=Yellow light fault detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.yellowFaultDetection.range=Yellow light fault detection downgrade report configuration valid range is [0,4]
juncer.param.lampFaultDetect.greenFaultDetection.notNull=Green light fault detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.greenFaultDetection.range=Green light fault detection downgrade report configuration valid range is [0,4]
juncer.param.lampFaultDetect.redYellowDetection.notNull=Red-yellow simultaneous detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.redYellowDetection.range=Red-yellow simultaneous detection downgrade report configuration valid range is [0,4]
juncer.param.lampFaultDetect.yellowGreenDetection.notNull=Yellow-green simultaneous detection downgrade report configuration cannot be empty
juncer.param.lampFaultDetect.yellowGreenDetection.range=Yellow-green simultaneous detection downgrade report configuration valid range is [0,4]

# Constraint validation messages
juncer.param.constraint.minGreenConstraintLower.notNull=Minimum green lower constraint cannot be empty
juncer.param.constraint.minGreenConstraintLower.range=Minimum green lower constraint valid range is [0,30]
juncer.param.constraint.minGreenConstraintUpper.notNull=Minimum green upper constraint cannot be empty
juncer.param.constraint.minGreenConstraintUpper.range=Minimum green upper constraint valid range is [0,30]
juncer.param.constraint.maxGreenConstraintLower.notNull=Maximum green lower constraint cannot be empty
juncer.param.constraint.maxGreenConstraintLower.range=Maximum green lower constraint valid range is [0,255]
juncer.param.constraint.maxGreenConstraintUpper.notNull=Maximum green upper constraint cannot be empty
juncer.param.constraint.maxGreenConstraintUpper.range=Maximum green upper constraint valid range is [0,255]
juncer.param.constraint.pedFlashingGreenLower.notNull=Pedestrian flashing green lower constraint cannot be empty
juncer.param.constraint.pedFlashingGreenLower.range=Pedestrian flashing green lower constraint valid range is [0,32]
juncer.param.constraint.pedFlashingGreenUpper.notNull=Pedestrian flashing green upper constraint cannot be empty
juncer.param.constraint.pedFlashingGreenUpper.range=Pedestrian flashing green upper constraint valid range is [0,32]
juncer.param.constraint.interGreenLower.notNull=Inter-green lower constraint cannot be empty
juncer.param.constraint.interGreenLower.range=Inter-green lower constraint valid range is [0,30]
juncer.param.constraint.interGreenUpper.notNull=Inter-green upper constraint cannot be empty
juncer.param.constraint.interGreenUpper.range=Inter-green upper constraint valid range is [0,30]
juncer.param.constraint.extGreenLower.notNull=Green extension lower constraint cannot be empty
juncer.param.constraint.extGreenLower.range=Green extension lower constraint valid range is [0,150]
juncer.param.constraint.extGreenUpper.notNull=Green extension upper constraint cannot be empty
juncer.param.constraint.extGreenUpper.range=Green extension upper constraint valid range is [0,150]
juncer.param.constraint.minGreenConstraint.valid=Minimum green lower constraint must be less than minimum green upper constraint
juncer.param.constraint.maxGreenConstraint.valid=Maximum green lower constraint must be less than maximum green upper constraint
juncer.param.constraint.pedFlashingGreenConstraint.valid=Pedestrian flashing green lower constraint must be less than pedestrian flashing green upper constraint
juncer.param.constraint.interGreenConstraint.valid=Inter-green lower constraint must be less than inter-green upper constraint
juncer.param.constraint.extGreenConstraint.valid=Green extension lower constraint must be less than green extension upper constraint

# CountdownInfo validation messages
juncer.param.countdownInfo.enableType.notNull=Countdown enable type cannot be empty
juncer.param.countdownInfo.enableType.range=Countdown enable type valid range is [0,3]
juncer.param.countdownInfo.baudRate.notNull=Communication baud rate cannot be empty
juncer.param.countdownInfo.baudRate.range=Communication baud rate valid range is [0,3]
juncer.param.countdownInfo.parity.notNull=Parity bit cannot be empty
juncer.param.countdownInfo.parity.range=Parity bit valid range is [0,2]
juncer.param.countdownInfo.pulseBrightWidth.notNull=Pulse bright screen width cannot be empty
juncer.param.countdownInfo.pulseBrightWidth.range=Pulse bright screen width valid range is [0,5]
juncer.param.countdownInfo.pulseBrightColor.notNull=Pulse bright screen color cannot be empty
juncer.param.countdownInfo.pulseBrightColor.range=Pulse bright screen color valid range is [0,1]
juncer.param.countdownInfo.pulseDarkWidth.notNull=Pulse dark screen width cannot be empty
juncer.param.countdownInfo.pulseDarkWidth.range=Pulse dark screen width valid range is [0,5]
juncer.param.countdownInfo.pulseDarkColor.notNull=Pulse dark screen color cannot be empty
juncer.param.countdownInfo.pulseDarkColor.range=Pulse dark screen color valid range is [0,2]
juncer.param.countdownInfo.redVehicleFlag.notNull=Red light countdown vehicle flag cannot be empty
juncer.param.countdownInfo.redVehicleFlag.range=Red light countdown vehicle flag valid range is [0,1]
juncer.param.countdownInfo.redVehicleTime.notNull=Red light countdown vehicle remaining time cannot be empty
juncer.param.countdownInfo.redVehicleTime.range=Red light countdown vehicle remaining time valid range is [1,20]
juncer.param.countdownInfo.greenVehicleFlag.notNull=Green light countdown vehicle flag cannot be empty
juncer.param.countdownInfo.greenVehicleFlag.range=Green light countdown vehicle flag valid range is [0,1]
juncer.param.countdownInfo.greenVehicleTime.notNull=Green light countdown vehicle remaining time cannot be empty
juncer.param.countdownInfo.greenVehicleTime.range=Green light countdown vehicle remaining time valid range is [1,20]
juncer.param.countdownInfo.redPedestrianFlag.notNull=Red light countdown pedestrian flag cannot be empty
juncer.param.countdownInfo.redPedestrianFlag.range=Red light countdown pedestrian flag valid range is [0,1]
juncer.param.countdownInfo.redPedestrianTime.notNull=Red light countdown pedestrian remaining time cannot be empty
juncer.param.countdownInfo.redPedestrianTime.range=Red light countdown pedestrian remaining time valid range is [1,20]
juncer.param.countdownInfo.greenPedestrianFlag.notNull=Green light countdown pedestrian flag cannot be empty
juncer.param.countdownInfo.greenPedestrianFlag.range=Green light countdown pedestrian flag valid range is [0,1]
juncer.param.countdownInfo.greenPedestrianTime.notNull=Green light countdown pedestrian remaining time cannot be empty
juncer.param.countdownInfo.greenPedestrianTime.range=Green light countdown pedestrian remaining time valid range is [1,20]
juncer.param.countdownInfo.inductionFlag.notNull=Induction mode countdown flag cannot be empty
juncer.param.countdownInfo.inductionFlag.range=Induction mode countdown flag valid range is [0,1]

# CountdownDisplayParam validation messages
juncer.param.countdownDisplayParam.countdownDisplayNo.notNull=Countdown display number cannot be empty
juncer.param.countdownDisplayParam.countdownDisplayNo.range=Countdown display number valid range is [1,32]
juncer.param.countdownDisplayParam.phase1.notNull=Phase 1 cannot be empty
juncer.param.countdownDisplayParam.phase1.range=Phase 1 valid range is [0,64]
juncer.param.countdownDisplayParam.phase2.notNull=Phase 2 cannot be empty
juncer.param.countdownDisplayParam.phase2.range=Phase 2 valid range is [0,64]

# SegmentAction validation messages
juncer.param.segmentAction.functionNo.notNull=Function number cannot be empty
juncer.param.segmentAction.param1.notNull=Parameter 1 cannot be empty
juncer.param.segmentAction.param2.notNull=Parameter 2 cannot be empty

# SegmentParam validation messages
juncer.param.segmentParam.startTime.notNull=Start time cannot be empty
juncer.param.segmentParam.startTime.range=Start time valid range is [0,1439]
juncer.param.segmentParam.planNo.notNull=Plan number cannot be empty
juncer.param.segmentParam.planNo.range=Plan number valid range is [1,128]
juncer.param.segmentParam.controlMode.notNull=Control mode cannot be empty
juncer.param.segmentParam.controlMode.range=Control mode valid range is [0,255]
juncer.param.segmentParam.coordCorrectionMode.notNull=Plan transition mode cannot be empty
juncer.param.segmentParam.coordCorrectionMode.range=Plan transition mode valid range is [0,3]
juncer.param.segmentParam.coordDirection.notNull=Coordination direction cannot be empty
juncer.param.segmentParam.coordDirection.range=Coordination direction valid range is [0,255]
juncer.param.segmentParam.actionNum.notNull=Action chain count cannot be empty
juncer.param.segmentParam.actionNum.range=Action chain count valid range is [0,16]
juncer.param.segmentParam.segmentActions.notNull=Action chain cannot be empty
juncer.param.segmentParam.segmentActions.size=Action chain count valid range is [0,16]

# Emergency validation messages
juncer.param.emergency.emergencyNo.notNull=Emergency number cannot be empty
juncer.param.emergency.emergencyNo.range=Emergency number valid range is [1,64]
juncer.param.emergency.stageNo.notNull=Emergency phase stage cannot be empty
juncer.param.emergency.stageNo.range=Emergency phase stage valid range is [1,64]
juncer.param.emergency.priority.notNull=Emergency priority cannot be empty
juncer.param.emergency.priority.range=Emergency priority valid range is [0,255]
juncer.param.emergency.delayTime.notNull=Delay time cannot be empty
juncer.param.emergency.delayTime.range=Delay time valid range is [0,255]
juncer.param.emergency.durationTime.notNull=Duration time cannot be empty
juncer.param.emergency.durationTime.range=Duration time valid range is [0,255]
juncer.param.emergency.callIntervalTime.notNull=Call interval time cannot be empty
juncer.param.emergency.callIntervalTime.range=Call interval time valid range is [0,255]
juncer.param.emergency.paramTypeCall.notNull=Demand variable type cannot be empty
juncer.param.emergency.paramNoCall.notNull=Demand variable number cannot be empty
juncer.param.emergency.paramTypeCancel.notNull=Cancel variable type cannot be empty
juncer.param.emergency.paramNoCancel.notNull=Cancel variable number cannot be empty
juncer.param.emergency.paramTypeConfirm.notNull=Confirm variable type cannot be empty
juncer.param.emergency.paramNoConfirm.notNull=Confirm variable number cannot be empty
juncer.param.emergency.callWatchDog.notNull=Emergency priority call watchdog cannot be empty
juncer.param.emergency.callWatchDog.range=Emergency priority call watchdog valid range is [0,255]

# ManualPanel validation messages
juncer.param.manualPanel.buttonNo.notNull=Manual button number cannot be empty
juncer.param.manualPanel.buttonNo.range=Manual button number valid range is [1,8]
juncer.param.manualPanel.callStageNo.notNull=Call stage number cannot be empty
juncer.param.manualPanel.callStageNo.size=Call stage number count must be 8
juncer.param.manualPanel.callStageNo.range=Call stage number valid range is [0,64]

# IncompatiblePhase validation messages
juncer.param.incompatiblePhase.phaseNo.notNull=Phase number cannot be empty
juncer.param.incompatiblePhase.phaseNo.range=Phase number valid range is [1,64]
juncer.param.incompatiblePhase.incompatiblePhaseSeq.notNull=Incompatible phase sequence cannot be empty
juncer.param.incompatiblePhase.incompatiblePhaseSeq.size=Incompatible phase sequence length must be [1,64]
juncer.param.incompatiblePhase.incompatiblePhaseSeq.range=Incompatible phase sequence valid range is [0,3]

# IncompatiblePhases validation messages
juncer.param.incompatiblePhases.incompatiblePhases.notNull=Phase conflict configuration list cannot be empty
juncer.param.incompatiblePhases.incompatiblePhases.size=Phase conflict configuration list length must be [1,64]

# InterGreenTime validation messages
juncer.param.interGreenTime.phaseNo.notNull=Phase number cannot be empty
juncer.param.interGreenTime.phaseNo.range=Phase number valid range is [1,64]
juncer.param.interGreenTime.interGreenTimeSeq.notNull=Inter-green time sequence cannot be empty
juncer.param.interGreenTime.interGreenTimeSeq.size=Inter-green time sequence length must be 64
juncer.param.interGreenTime.interGreenTimeSeq.range=Inter-green time sequence valid range is [0,30]

# InterGreenTimeTable validation messages
juncer.param.interGreenTimeTable.interGreenTimeTableNo.notNull=Inter-green time table number cannot be empty
juncer.param.interGreenTimeTable.interGreenTimeTableNo.range=Inter-green time table number valid range is [1,4]
juncer.param.interGreenTimeTable.interGreenTimes.notNull=Inter-green time configuration list cannot be empty
juncer.param.interGreenTimeTable.interGreenTimes.size=Inter-green time configuration list length must be 64

# UIThreshold validation messages
juncer.param.uiThreshold.voltageThresholdUpper.notNull=Voltage threshold upper limit cannot be empty
juncer.param.uiThreshold.voltageThresholdUpper.range=Voltage threshold upper limit valid range is [0,255]
juncer.param.uiThreshold.voltageThresholdLower.notNull=Voltage threshold lower limit cannot be empty
juncer.param.uiThreshold.voltageThresholdLower.range=Voltage threshold lower limit valid range is [0,255]
juncer.param.uiThreshold.currentThresholdUpper.notNull=Current threshold upper limit cannot be empty
juncer.param.uiThreshold.currentThresholdUpper.range=Current threshold upper limit valid range is [0,255]
juncer.param.uiThreshold.currentThresholdLower.notNull=Current threshold lower limit cannot be empty
juncer.param.uiThreshold.currentThresholdLower.range=Current threshold lower limit valid range is [0,255]
juncer.param.uiThreshold.voltageThreshold.valid=Voltage threshold upper limit must be greater than lower limit
juncer.param.uiThreshold.currentThreshold.valid=Current threshold upper limit must be greater than lower limit

# Threshold validation messages
juncer.param.threshold.voltageUpper.notNull=Voltage threshold upper limit cannot be empty
juncer.param.threshold.voltageLower.notNull=Voltage threshold lower limit cannot be empty
juncer.param.threshold.currentUpper.notNull=Current threshold upper limit cannot be empty
juncer.param.threshold.currentLower.notNull=Current threshold lower limit cannot be empty
juncer.param.threshold.voltage.upperGreaterThanLower=Voltage upper threshold must be greater than voltage lower threshold
juncer.param.threshold.current.upperGreaterThanLower=Current upper threshold must be greater than current lower threshold
juncer.param.threshold.voltageUpper.range=Voltage threshold upper limit must be between [0,510]
juncer.param.threshold.voltageLower.range=Voltage threshold lower limit must be between [0,510]
juncer.param.threshold.currentUpper.range=Current threshold upper limit must be between [0,3000]
juncer.param.threshold.currentLower.range=Current threshold lower limit must be between [0,3000]

# LightTransition validation messages
juncer.param.lightTransition.colorType.notNull=Light color type cannot be empty
juncer.param.lightTransition.colorType.range=Light color type valid range is [0,14]
juncer.param.lightTransition.colorTime.notNull=Light color duration cannot be empty
juncer.param.lightTransition.colorTime.range=Light color duration valid range is [0,99]

# UserFlag validation messages
juncer.param.userFlag.userFlagNo.notNull=User flag number cannot be empty
juncer.param.userFlag.userFlagNo.range=User flag number valid range is [1,64]
juncer.param.userFlag.userFlagName.notNull=User flag name cannot be empty

# Timer validation messages
juncer.param.timer.timerNo.notNull=Timer number cannot be empty
juncer.param.timer.timerNo.range=Timer number valid range is [1,64]
juncer.param.timer.timerName.notNull=Timer name cannot be empty
juncer.param.timer.interval.notNull=Time interval cannot be empty
juncer.param.timer.interval.range=Time interval valid range is [0,65535]

# LogicOutput validation messages
juncer.param.logicOutput.logicOutputNo.notNull=Logic output number cannot be empty
juncer.param.logicOutput.logicOutputNo.range=Logic output number valid range is [1,64]
juncer.param.logicOutput.logicOutputName.notNull=Logic output name cannot be empty

# Demand validation messages
juncer.param.demand.inputType.notNull=Input type cannot be empty
juncer.param.demand.inputNo.notNull=Input number cannot be empty
juncer.param.demand.inputFlag.notNull=Parameter flag cannot be empty

# Extension validation messages
juncer.param.extension.inputType.notNull=Input type cannot be empty
juncer.param.extension.inputNo.notNull=Input number cannot be empty
juncer.param.extension.inputFlag.notNull=Parameter flag cannot be empty

# StageTransitionConstraint validation messages
juncer.param.stageTransitionConstraint.stageNo.notNull=Phase stage number cannot be empty
juncer.param.stageTransitionConstraint.stageNo.range=Phase stage number valid range is [1,64]
juncer.param.stageTransitionConstraint.transitionConstraints.notNull=Phase stage transition constraints cannot be empty
juncer.param.stageTransitionConstraint.transitionConstraints.size=Phase stage transition constraints length must be 64
juncer.param.stageTransitionConstraint.transitionConstraints.range=Phase stage transition constraints valid range is [0,64] and 255

# StageParamInfo validation messages
basic.param.stageParam.demandsInsertLeavingHurryCall.notNull=Demand insertion after emergency call end cannot be empty
basic.param.stageParam.demandsInsertLeavingHurryCall.range=Demand insertion after emergency call end must be between [0,1]
basic.param.stageParam.demandsInsertLeavingSystem.notNull=Demand insertion after system control end cannot be empty
basic.param.stageParam.demandsInsertLeavingSystem.range=Demand insertion after system control end must be between [0,1]
basic.param.stageParam.unconditionalDemand.notNull=Unconditional default demand cannot be empty
basic.param.stageParam.unconditionalDemand.range=Unconditional default demand must be between [0,1]

