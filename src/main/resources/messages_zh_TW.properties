# 基礎參數驗證消息
# 區域編號
basic.param.noArea.notNull=區域編號不能為空
basic.param.noArea.range=區域編號有效範圍是[0,39]

# 路口編號
basic.param.noJunc.notNull=路口編號不能為空
basic.param.noJunc.range=路口編號有效範圍是[0,499]

# 信號機型
basic.param.type.notNull=信號機型不能為空

# 出廠日期
basic.param.productionDate.notNull=出廠日期不能為空

# 經度
basic.param.lon.notNull=經度不能為空

# 緯度
basic.param.lat.notNull=緯度不能為空

basic.param.cmmType.match=通信類型與IP信息不匹配

# 控制路口數
basic.param.controlledJunctionNum.notNull=控制路口數不能為空
basic.param.controlledJunctionNum.range=控制路口有效範圍是[1,8]

# 安裝路口
basic.param.installIntersection.notNull=安裝路口不能為空

# 通訊模式
basic.param.ipEnabled.notNull=通訊模式不能為空
basic.param.ipEnabled.range=通訊模式有效範圍是[0,2]

# MAC地址
basic.param.macAddress.notNull=信號機mac地址不能為空
basic.param.macAddress.invalid=無效的MAC地址

# IPv4信息
basic.param.ipv4.notNull=信號機ipv4信息不能為空
basic.param.ipv4.ip.notNull=ipv4地址不能為空
basic.param.ipv4.ip.invalid=必須是有效的IPv4地址
basic.param.ipv4.mask.notNull=子網掩碼不能為空
basic.param.ipv4.mask.invalid=必須是有效的子網掩碼
basic.param.ipv4.gateway.notNull=網關不能為空
basic.param.ipv4.gateway.invalid=必須是有效的網關

# IPv6信息
basic.param.ipv6.notNull=信號機ipv6信息不能為空
basic.param.ipv6.ip.notNull=ipv6地址不能為空
basic.param.ipv6.ip.invalid=必須是有效的IPv6地址
basic.param.ipv6.mask.notNull=ipv6子網掩碼不能為空
basic.param.ipv6.mask.invalid=必須是有效的IPv6子網掩碼
basic.param.ipv6.gateway.notNull=ipv6網關不能為空
basic.param.ipv6.gateway.invalid=必須是有效的IPv6網關

# 上位機IPv4配置
basic.param.hostIpv4s.notNull=上位機ipv4配置數組不能為空
basic.param.hostIpv4s.size=上位機ipv4配置數組有效範圍是[0,4]

# 上位機IPv6配置
basic.param.hostIpv6s.notNull=上位機ipv6配置數組不能為空
basic.param.hostIpv6s.size=上位機ipv6配置數組有效範圍是[0,4]

# 上位機IP地址信息
basic.param.hostIp.enabled.notNull=是否啟用不能為空
basic.param.hostIp.enabled.range=是否啟用的有效範圍是[0,1]
basic.param.hostIp.ip.notNull=地址不能為空
basic.param.hostIp.ip.invalid=必須是有效的IP地址
basic.param.hostIp.port.notNull=端口不能為空
basic.param.hostIp.port.range=端口的有效範圍是(0,65535]
basic.param.hostIp.commType.notNull=通信類型不能為空
basic.param.hostIp.commType.range=通信類型的有效範圍是[1,3]
basic.param.hostIp.protoType.notNull=協議類型不能為空
basic.param.hostIp.protoType.range=協議類型的有效範圍是[1,5]

# 燈檢測閾值配置
basic.param.lampFaultThre.lampGroupNo.notNull=燈組編號不能為空
basic.param.lampFaultThre.lampGroupNo.range=燈組編號有效範圍[1,64]
basic.param.lampFaultThre.greenThreshold.notNull=綠燈電壓電流閾值配置不能為空
basic.param.lampFaultThre.yellowThreshold.notNull=黃燈電壓電流閾值配置不能為空
basic.param.lampFaultThre.redThreshold.notNull=紅燈電壓電流閾值配置不能為空

# 燈組信息
basic.param.lampGroup.lampGroupNo.notNull=燈組編號不能為空
basic.param.lampGroup.lampGroupNo.range=燈組編號有效範圍[1,64]
basic.param.lampGroup.crossingSeqNo.notNull=子路口號不能為空
basic.param.lampGroup.crossingSeqNo.range=子路口號有效範圍[0,8]
basic.param.lampGroup.detectFlag.notNull=檢測標記不能為空
basic.param.lampGroup.detectFlag.range=檢測標記有效範圍[0,1]
basic.param.lampGroup.type.notNull=燈組類型不能為空
basic.param.lampGroup.direction.notNull=方向不能為空
basic.param.lampGroup.name.notNull=燈組名稱不能為空

# 燈組參數信息
basic.param.lampGroupParam.lampGroups.notNull=燈組信息列表不能為空
basic.param.lampGroupParam.lampGroups.size=燈組信息列表個數應該為[1,64]
basic.param.lampGroupParam.uiThreshold.notNull=燈檢測電壓電流閾值配置不能為空
basic.param.lampGroupParam.lampFaultThres.notNull=燈檢測閾值配置不能為空

# 邏輯輸入信息
basic.param.logicInput.logicInputNo.notNull=邏輯輸入編號不能為空
basic.param.logicInput.logicInputNo.min=邏輯輸入編號不能小於1
basic.param.logicInput.logicInputNo.max=邏輯輸入編號不能大於64
basic.param.logicInput.logicInputName.notNull=邏輯輸入名稱不能為空
basic.param.logicInput.installLocation.notNull=安裝位置不能為空
basic.param.logicInput.state.notNull=狀態信號不能為空
basic.param.logicInput.state.min=狀態信號不能小於0
basic.param.logicInput.state.max=狀態信號不能大於2
basic.param.logicInput.hold.notNull=需求記憶不能為空
basic.param.logicInput.hold.min=需求記憶不能小於0
basic.param.logicInput.hold.max=需求記憶不能大於1
basic.param.logicInput.duration.notNull=持續時長不能為空
basic.param.logicInput.duration.min=持續時長不能小於0
basic.param.logicInput.duration.max=持續時長不能大於65535
basic.param.logicInput.invert.notNull=取反不能為空
basic.param.logicInput.invert.min=取反不能小於0
basic.param.logicInput.invert.max=取反不能大於1
basic.param.logicInput.pairedDetector.notNull=配對檢測器不能為空
basic.param.logicInput.pairedDetector.min=配對檢測器不能小於0
basic.param.logicInput.pairedDetector.max=配對檢測器不能大於64
basic.param.logicInput.pairedDetectorSpacing.notNull=配對檢測器距離不能為空
basic.param.logicInput.pairedDetectorSpacing.min=配對檢測器距離不能小於0
basic.param.logicInput.pairedDetectorSpacing.max=配對檢測器距離不能大於65535
basic.param.logicInput.ped.notNull=行人檢測不能為空
basic.param.logicInput.ped.min=行人檢測不能小於0
basic.param.logicInput.ped.max=行人檢測不能大於1
basic.param.logicInput.volume.notNull=流量檢測不能為空
basic.param.logicInput.volume.min=流量檢測不能小於0
basic.param.logicInput.volume.max=流量檢測不能大於1
basic.param.logicInput.occupancy.notNull=佔有率檢測不能為空
basic.param.logicInput.occupancy.min=佔有率檢測不能小於0
basic.param.logicInput.occupancy.max=佔有率檢測不能大於1
basic.param.logicInput.speed.notNull=速度檢測不能為空
basic.param.logicInput.speed.min=速度檢測不能小於0
basic.param.logicInput.speed.max=速度檢測不能大於1
basic.param.logicInput.queue.notNull=排隊檢測不能為空
basic.param.logicInput.queue.min=排隊檢測不能小於0
basic.param.logicInput.queue.max=排隊檢測不能大於1
basic.param.logicInput.count.notNull=數量檢測不能為空
basic.param.logicInput.count.min=數量檢測不能小於0
basic.param.logicInput.count.max=數量檢測不能大於1
basic.param.logicInput.identity.notNull=身份檢測不能為空
basic.param.logicInput.identity.min=身份檢測不能小於0
basic.param.logicInput.identity.max=身份檢測不能大於1
basic.param.logicInput.maxPresence.notNull=有效時間不能為空
basic.param.logicInput.maxPresence.min=有效時間不能小於0
basic.param.logicInput.maxPresence.max=有效時間不能大於65535
basic.param.logicInput.noActivity.notNull=無效時間不能為空
basic.param.logicInput.noActivity.min=無效時間不能小於0
basic.param.logicInput.noActivity.max=無效時間不能大於65535
basic.param.logicInput.extend.notNull=延長時間不能為空
basic.param.logicInput.extend.min=延長時間不能小於0
basic.param.logicInput.extend.max=延長時間不能大於255
basic.param.logicInput.delay.notNull=延遲時間不能為空
basic.param.logicInput.delay.min=延遲時間不能小於0
basic.param.logicInput.delay.max=延遲時間不能大於65535
basic.param.logicInput.failOperation.notNull=故障動作不能為空
basic.param.logicInput.failOperation.min=故障動作不能小於0
basic.param.logicInput.failOperation.max=故障動作不能大於1

# 方案階段參數
basic.param.planStage.stageNo.notNull=階段編號不能為空
basic.param.planStage.stageNo.min=階段編號不能小於1
basic.param.planStage.stageNo.max=階段編號不能大於64
basic.param.planStage.stageTime.notNull=階段時長不能為空
basic.param.planStage.stageTime.min=階段時長不能小於0
basic.param.planStage.stageTime.max=階段時長不能大於65535
basic.param.planStage.stageActivationType.notNull=階段出現類型不能為空
basic.param.planStage.stageActivationType.min=階段出現類型不能小於1
basic.param.planStage.stageActivationType.max=階段出現類型不能大於3
basic.param.planStage.coordinatedForceOff.notNull=協調強制關閉不能為空
basic.param.planStage.coordinatedForceOff.min=協調強制關閉不能小於1
basic.param.planStage.coordinatedForceOff.max=協調強制關閉不能大於2
basic.param.planStage.planStagePhaseParams.notNull=相位運行早截斷參數不能為空
basic.param.planStage.planStagePhaseParams.size=相位運行早截斷參數個數應在0到64之間

# 相位運行早截斷參數
basic.param.planStagePhase.phaseNo.notNull=相位編號不能為空
basic.param.planStagePhase.phaseNo.min=相位編號不能小於1
basic.param.planStagePhase.phaseNo.max=相位編號不能大於64
basic.param.planStagePhase.laggingTime.notNull=遲後時長不能為空
basic.param.planStagePhase.laggingTime.min=遲後時長不能小於0
basic.param.planStagePhase.laggingTime.max=遲後時長不能大於255
basic.param.planStagePhase.delayCutOffTime.notNull=遲截時長不能為空
basic.param.planStagePhase.delayCutOffTime.min=遲截時長不能小於0
basic.param.planStagePhase.delayCutOffTime.max=遲截時長不能大於255

# 階段相位參數信息
basic.param.stageParam.stageNo.notNull=階段編號不能為空
basic.param.stageParam.stageNo.min=階段編號不能小於1
basic.param.stageParam.stageNo.max=階段編號不能大於64
basic.param.stageParam.stageName.notNull=階段名稱不能為空
basic.param.stageParam.demandsInsertLeavingManualAndFixStage.notNull=結束手動運行期時插入階段需求不能為空
basic.param.stageParam.demandsInsertLeavingManualAndFixStage.discrete=結束手動運行期時插入階段需求只能為0或1
basic.param.stageParam.demandsInsertStartUpStage.notNull=啟動時插入階段需求不能為空
basic.param.stageParam.demandsInsertStartUpStage.discrete=啟動時插入階段需求只能為0或1
basic.param.stageParam.windowsTime.notNull=窗口時間不能為空
basic.param.stageParam.windowsTime.min=窗口時間不能小於0
basic.param.stageParam.windowsTime.max=窗口時間不能大於255
basic.param.stageParam.stagePhaseParams.notNull=相位信息不能為空
basic.param.stageParam.stagePhaseParams.size=相位信息長度應在0到64之間

# 階段相位參數
basic.param.stagePhase.phaseNo.notNull=相位編號不能為空
basic.param.stagePhase.phaseNo.min=相位編號不能小於1
basic.param.stagePhase.phaseNo.max=相位編號不能大於64
basic.param.stagePhase.demand.notNull=相位出現類型不能為空
basic.param.stagePhase.demand.discrete=相位出現類型只能為1或2
basic.param.stagePhase.laggingTime.notNull=遲後時長不能為空
basic.param.stagePhase.laggingTime.min=遲後時長不能小於0
basic.param.stagePhase.laggingTime.max=遲後時長不能大於255
basic.param.stagePhase.delayCutOffTime.notNull=遲截時長不能為空
basic.param.stagePhase.delayCutOffTime.min=遲截時長不能小於0
basic.param.stagePhase.delayCutOffTime.max=遲截時長不能大於255

# 階段轉移表
basic.param.stageTransTable.stageTransTableNo.notNull=階段轉移表編號不能為空
basic.param.stageTransTable.stageTransTableNo.range=階段轉移表編號有效範圍[1,4]
basic.param.stageTransTable.stageTransConstraint.notNull=階段轉移參數不能為空
basic.param.stageTransTable.stageTransConstraint.size=階段轉移參數個數應為[1,64]

# 啟動階段參數
basic.param.startupStage.crossingSeqNo.notNull=子路口號不能為空
basic.param.startupStage.crossingSeqNo.min=子路口號不能小於1
basic.param.startupStage.crossingSeqNo.max=子路口號不能大於8
basic.param.startupStage.noDemandStage.notNull=無需求進入階段不能為空
basic.param.startupStage.noDemandStage.min=無需求進入階段不能小於0
basic.param.startupStage.noDemandStage.max=無需求進入階段不能大於64
basic.param.startupStage.startupStage.notNull=啟動進入階段不能為空
basic.param.startupStage.startupStage.min=啟動進入階段不能小於0
basic.param.startupStage.startupStage.max=啟動進入階段不能大於64

# 相位參數
basic.param.actuatedPhase.phaseNo.notNull=相位編號不能為空
basic.param.actuatedPhase.phaseNo.min=相位編號不能小於1
basic.param.actuatedPhase.phaseNo.max=相位編號不能大於64
basic.param.actuatedPhase.phasePassage.notNull=相位通過時間參數不能為空
basic.param.actuatedPhase.phasePassage.min=相位通過時間參數不能小於0
basic.param.actuatedPhase.phasePassage.max=相位通過時間參數不能大於255
basic.param.actuatedPhase.phaseAddedInitial.notNull=相位可變初始綠參數不能為空
basic.param.actuatedPhase.phaseAddedInitial.min=相位可變初始綠參數不能小於0
basic.param.actuatedPhase.phaseAddedInitial.max=相位可變初始綠參數不能大於255
basic.param.actuatedPhase.phaseMaximumInitial.notNull=相位最大初始綠參數不能為空
basic.param.actuatedPhase.phaseMaximumInitial.min=相位最大初始綠參數不能小於0
basic.param.actuatedPhase.phaseMaximumInitial.max=相位最大初始綠參數不能大於255
basic.param.actuatedPhase.phaseTimeBeforeReduction.notNull=相位通過時間調整前時間參數不能為空
basic.param.actuatedPhase.phaseTimeBeforeReduction.min=相位通過時間調整前時間參數不能小於0
basic.param.actuatedPhase.phaseTimeBeforeReduction.max=相位通過時間調整前時間參數不能大於255
basic.param.actuatedPhase.phaseTimeToReduce.notNull=相位通過時間調整時相位通過時間參數不能為空
basic.param.actuatedPhase.phaseTimeToReduce.min=相位通過時間調整時相位通過時間參數不能小於0
basic.param.actuatedPhase.phaseTimeToReduce.max=相位通過時間調整時相位通過時間參數不能大於255
basic.param.actuatedPhase.phaseReduceBy.notNull=相位車輛間隔參數不能為空
basic.param.actuatedPhase.phaseReduceBy.min=相位車輛間隔參數不能小於0
basic.param.actuatedPhase.phaseReduceBy.max=相位車輛間隔參數不能大於255
basic.param.actuatedPhase.phaseMinimumGap.notNull=相位車輛最小間隔參數不能為空
basic.param.actuatedPhase.phaseMinimumGap.min=相位車輛最小間隔參數不能小於0
basic.param.actuatedPhase.phaseMinimumGap.max=相位車輛最小間隔參數不能大於255
basic.param.actuatedPhase.phaseDynamicMaxLimit.notNull=相位動態最大值不能為空
basic.param.actuatedPhase.phaseDynamicMaxLimit.min=相位動態最大值不能小於0
basic.param.actuatedPhase.phaseDynamicMaxLimit.max=相位動態最大值不能大於255
basic.param.actuatedPhase.phaseDynamicMaxStep.notNull=相位動態最大值步長不能為空
basic.param.actuatedPhase.phaseDynamicMaxStep.min=相位動態最大值步長不能小於0
basic.param.actuatedPhase.phaseDynamicMaxStep.max=相位動態最大值步長不能大於255

# 條件參數
basic.param.conditioning.conditionings.notNull=條件運算列表不能為空
basic.param.conditioning.conditionings.size=條件運算列表個數應在1到64之間
basic.param.conditioning.actions.notNull=動作列表不能為空
basic.param.conditioning.actions.size=動作列表個數應在1到64之間

# 控制器參數
basic.param.controller.deviceVersion.notNull=設備版本不能為空
basic.param.controller.controlBoardSerialNumber.notNull=主控板序列號不能為空
basic.param.controller.lampDriverBoardMaxNumber.notNull=燈驅板數不能為空
basic.param.controller.lampDriverBoardMaxNumber.min=燈驅板最小個數為1
basic.param.controller.lampDriverBoardMaxNumber.max=燈驅板最大個數為16
basic.param.controller.lampDriverBoards.notNull=燈驅板信息不能為空
basic.param.controller.lampDriverBoards.size=燈驅板信息個數應該為16
basic.param.controller.detectionBoardMaxNumber.notNull=檢測板數不能為空
basic.param.controller.detectionBoardMaxNumber.min=檢測板最小個數為0
basic.param.controller.detectionBoardMaxNumber.max=檢測板最大個數為4
basic.param.controller.detectionBoards.notNull=檢測板信息不能為空
basic.param.controller.detectionBoards.size=檢測板信息個數應該為4
basic.param.controller.informationBoardMaxNumber.notNull=信息板數不能為空
basic.param.controller.informationBoardMaxNumber.min=信息板最小個數為0
basic.param.controller.informationBoardMaxNumber.max=信息板最大個數為1
basic.param.controller.informationBoards.notNull=信息板信息不能為空
basic.param.controller.informationBoards.size=信息板信息個數應該為1
basic.param.controller.systemCordRef.notNull=系統協調零點不能為空
basic.param.controller.systemCordRef.range=系統協調零點有效範圍[0,1440)
basic.param.controller.maxGreenWatchDog.notNull=看門狗最大綠不能為空
basic.param.controller.maxGreenWatchDog.range=看門狗最大綠有效範圍[0,255]
basic.param.controller.startInterGreen.notNull=啟動綠燈間隔不能為空
basic.param.controller.startInterGreen.range=啟動綠燈間隔有效範圍[0,32]
basic.param.controller.switchedSigns.notNull=可變標記個數不能為空
basic.param.controller.switchedSigns.range=可變標記有效範圍[0,32]
basic.param.controller.stageSkipProhibited.notNull=階段跳轉禁止不能為空
basic.param.controller.stageSkipProhibited.range=階段跳轉禁止有效範圍[0,1]
basic.param.controller.lampFaultDetect.notNull=故障響應不能為空
basic.param.controller.constraint.notNull=約束信息不能為空
basic.param.controller.modePriority.notNull=模式優先級不能為空
basic.param.controller.modePriority.size=模式優先級數組長度應該為255
basic.param.controller.lampGroupNumber.digitsInSet=燈組數必須是4、6或8
basic.param.controller.usedPhaseNum.notNull=使用相位數不能為空
basic.param.controller.usedPhaseNum.range=使用相位數有效範圍是[2,64]
basic.param.controller.usedStageNum.notNull=使用階段數不能為空
basic.param.controller.usedStageNum.range=使用階段數有效範圍是[1,64]
basic.param.controller.degradeAction.notNull=降級動作不能為空
basic.param.controller.degradeAction.range=降級動作有效範圍是[0,1]
basic.param.controller.hurryCallExecuteWatchDog.notNull=緊急調用執行看門狗不能為空
basic.param.controller.hurryCallExecuteWatchDog.range=緊急調用執行看門狗有效範圍是[0,255]
basic.param.controller.yellowAsWaitIndicator.notNull=黃燈作為等待指示器不能為空
basic.param.controller.yellowAsWaitIndicator.range=黃燈作為等待指示器有效範圍是[0,1]
basic.param.controller.maxManualControlTime.notNull=最大手動控制時間不能為空
basic.param.controller.maxManualControlTime.range=最大手動控制時間有效範圍[0,255]
basic.param.controller.flashOnTime.notNull=閃燈亮燈時長不能為空
basic.param.controller.flashOnTime.range=閃燈亮燈時長有效範圍[400,1000]
basic.param.controller.flashOffTime.notNull=閃燈滅燈時長不能為空
basic.param.controller.flashOffTime.range=閃燈滅燈時長有效範圍[400,1000]

# 倒計時參數
basic.param.countDown.countDown.notNull=倒計時配置不能為空
basic.param.countDown.countdownDisplayParams.notNull=倒計時顯示參數不能為空
basic.param.countDown.countdownDisplayParams.size=倒計時顯示參數個數應在1到32之間

# 日計劃參數
basic.param.dayPlan.dayPlanNo.notNull=日計劃編號不能為空
basic.param.dayPlan.dayPlanNo.min=日計劃編號不能小於1
basic.param.dayPlan.dayPlanNo.max=日計劃編號不能大於128
basic.param.dayPlan.crossingSeqNo.notNull=子路口號不能為空
basic.param.dayPlan.crossingSeqNo.min=子路口號不能小於1
basic.param.dayPlan.crossingSeqNo.max=子路口號不能大於8
basic.param.dayPlan.segmentParams.notNull=時段參數不能為空
basic.param.dayPlan.segmentParams.size=時段參數個數應在1到48之間
basic.param.dayPlan.segmentParams.startTime.order=時段參數的開始時間必須按升序排列
basic.param.dayPlan.dayPlanName.notNull=日計劃名稱不能為空

# 緊急參數
basic.param.emergency.emergencies.notNull=緊急列表不能為空
basic.param.emergency.emergencies.size=緊急列表個數應在1到64之間
basic.param.emergency.manualPanels.notNull=手控控制列表不能為空
basic.param.emergency.manualPanels.size=手控控制列表個數應該是8

# 相位衝突參數
basic.param.incompatiblePhase.incompatiblePhases.notNull=相位衝突表不能為空
basic.param.incompatiblePhase.incompatiblePhases.size=相位衝突表個數應為[1,64]

# 綠間隔參數
basic.param.interGreenTime.interGreenTimeTables.notNull=綠間隔表不能為空
basic.param.interGreenTime.interGreenTimeTables.size=綠間隔表個數應為4
basic.param.interGreenTime.modeTrans.notNull=模式綠間隔表不能為空
basic.param.interGreenTime.modeTrans.size=模式綠間隔表個數應為255
basic.param.interGreenTime.modeTrans.discreteValuesList=模式綠間隔表有效範圍為[1,4]
# 相位燈序參數
basic.param.lampSequence.lampSequenceNo.notNull=相位燈序編號不能為空
basic.param.lampSequence.lampSequenceNo.range=相位燈序編號有效範圍為[1,64]
basic.param.lampSequence.sequenceType.notNull=相位燈序類型不能為空
basic.param.lampSequence.sequenceType.range=相位燈序類型有效範圍為[0,3]
basic.param.lampSequence.sequenceName.notNull=相位燈序名稱不能為空
basic.param.lampSequence.partTimeColor.notNull=待機燈色不能為空
basic.param.lampSequence.loseLightTransitionStartups.notNull=開機失去路權配置不能為空
basic.param.lampSequence.loseLightTransitionStartups.size=開機失去路權配置數量必須為4
basic.param.lampSequence.obtainLightTransitionStartups.notNull=開機獲得路權配置不能為空
basic.param.lampSequence.obtainLightTransitionStartups.size=開機獲得路權配置數量必須為4
basic.param.lampSequence.loseLightTransitionYellowFlashs.notNull=正常到待機失去路權配置不能為空
basic.param.lampSequence.loseLightTransitionYellowFlashs.size=正常到待機失去路權配置數量必須為4
basic.param.lampSequence.obtainLightTransitionYellowFlashs.notNull=正常到待機獲得路權配置不能為空
basic.param.lampSequence.obtainLightTransitionYellowFlashs.size=正常到待機獲得路權配置數量必須為4
basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.notNull=待機到正常失去路權配置不能為空
basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.size=待機到正常失去路權配置數量必須為4
basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.notNull=待機到正常獲得路權配置不能為空
basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.size=待機到正常獲得路權配置數量必須為4

# 邏輯參數
basic.param.logic.logicInputs.notNull=邏輯輸入不能為空
basic.param.logic.logicInputs.size=邏輯輸入個數應在1到64之間
basic.param.logic.logicOutputs.notNull=邏輯輸出不能為空
basic.param.logic.logicOutputs.size=邏輯輸出個數應在1到64之間
basic.param.logic.timers.notNull=定時器不能為空
basic.param.logic.timers.size=定時器個數應在1到64之間
basic.param.logic.userFlags.notNull=用戶標誌列表不能為空
basic.param.logic.userFlags.size=用戶標誌列表個數應在1到64之間

# 相位需求擴展參數
basic.param.phaseDemandExtend.phaseNo.notNull=相位編號不能為空
basic.param.phaseDemandExtend.phaseNo.min=相位編號不能小於1
basic.param.phaseDemandExtend.phaseNo.max=相位編號不能大於64
basic.param.phaseDemandExtend.demands.notNull=需求列表不能為空
basic.param.phaseDemandExtend.demands.size=需求列表個數應為8
basic.param.phaseDemandExtend.extensions.notNull=延長列表不能為空
basic.param.phaseDemandExtend.extensions.size=延長列表個數應為8
basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.notNull=結束手動運行期時插入相位需求不能為空
basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.range=結束手動運行期時插入相位需求有效範圍為[0,1]
basic.param.phaseDemandExtend.demandsInsertStartUpPhase.notNull=啟動時插入相位需求不能為空
basic.param.phaseDemandExtend.demandsInsertStartUpPhase.range=啟動時插入相位需求有效範圍為[0,1]
basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.notNull=緊急調用結束後插入需求不能為空
basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.range=緊急調用結束後插入需求有效範圍為[0,1]
basic.param.phaseDemandExtend.demandsInsertLeavingSystem.notNull=中心控制結末後插入需求不能為空
basic.param.phaseDemandExtend.demandsInsertLeavingSystem.range=中心控制結末後插入需求有效範圍為[0,1]
basic.param.phaseDemandExtend.unconditionalDemand.notNull=無條件默認需求不能為空
basic.param.phaseDemandExtend.unconditionalDemand.range=無條件默認需求有效範圍為[0,1]
basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.notNull=非鎖定需求最大相位最大綠不能為空
basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.range=非鎖定需求最大相位最大綠有效範圍為[0,1]
basic.param.phaseDemandExtend.minGreenDemand.notNull=最小綠需求不能為空
basic.param.phaseDemandExtend.minGreenDemand.range=最小綠需求有效範圍為[0,1]
basic.param.phaseDemandExtend.maxGreenDemand.notNull=最大綠需求不能為空
basic.param.phaseDemandExtend.maxGreenDemand.range=最大綠需求有效範圍為[0,1]
basic.param.phaseDemandExtend.revertivePhaseDemand.notNull=相位最大綠跟隨相位需求不能為空
basic.param.phaseDemandExtend.revertivePhaseDemand.range=相位最大綠跟隨相位需求有效範圍為[0,64]

# 相位參數信息
basic.param.phaseInfo.phaseNo.notNull=相位編號不能為空
basic.param.phaseInfo.phaseNo.range=相位編號有效範圍是[1,64]
basic.param.phaseInfo.lampSequenceNo.notNull=相位燈序編號不能為空
basic.param.phaseInfo.lampSequenceNo.range=相位燈序編號有效範圍是[0,64]
basic.param.phaseInfo.lampSequenceNo.range2=相位燈序編號使用有效範圍是[0,64]
basic.param.phaseInfo.phaseLightsGroups.notNull=相位燈組不能為空
basic.param.phaseInfo.phaseLightsGroups.size=相位燈組長度有效範圍是64
basic.param.phaseInfo.phaseLightsGroups.range=相位燈組有效範圍是[0,1]
basic.param.phaseInfo.appearance.notNull=綠燈開始方式不能為空
basic.param.phaseInfo.appearance.range=綠燈開始方式有效範圍是[0,2]
basic.param.phaseInfo.termination.notNull=綠燈結束方式不能為空
basic.param.phaseInfo.assocPhase.notNull=關聯相位不能為空
basic.param.phaseInfo.assocPhase.range=關聯相位有效範圍是[0,64]
basic.param.phaseInfo.minGreenTime.notNull=相位最小綠時間1不能為空
basic.param.phaseInfo.minGreenTime.range=相位最小綠時間1有效範圍為[0,30]
basic.param.phaseInfo.maxGreenTime1.notNull=相位最大綠時間1不能為空
basic.param.phaseInfo.maxGreenTime1.range=相位最大綠時間1有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime2.notNull=相位最大綠時間2不能為空
basic.param.phaseInfo.maxGreenTime2.range=相位最大綠時間2有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime3.notNull=相位最大綠時間3不能為空
basic.param.phaseInfo.maxGreenTime3.range=相位最大綠時間3有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime4.notNull=相位最大綠時間4不能為空
basic.param.phaseInfo.maxGreenTime4.range=相位最大綠時間4有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime5.notNull=相位最大綠時間5不能為空
basic.param.phaseInfo.maxGreenTime5.range=相位最大綠時間5有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime6.notNull=相位最大綠時間6不能為空
basic.param.phaseInfo.maxGreenTime6.range=相位最大綠時間6有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime7.notNull=相位最大綠時間7不能為空
basic.param.phaseInfo.maxGreenTime7.range=相位最大綠時間7有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime8.notNull=相位最大綠時間8不能為空
basic.param.phaseInfo.maxGreenTime8.range=相位最大綠時間8有效範圍為[0,255]
basic.param.phaseInfo.maxGreenTime1.greaterThanMinGreen=最大綠燈時間1必須大於最小綠燈時間
basic.param.phaseInfo.maxGreenTime.sequential=最大綠燈時間必須從maxGreenTime1到maxGreenTime8依次遞增

# Phase parameters
basic.param.phase.phaseParams.notNull=相位參數列表不能為空
basic.param.phase.phaseParams.size=相位參數列表個數應為64

# Plan parameters
basic.param.plan.planNo.notNull=方案編號不能為空
basic.param.plan.planNo.min=方案編號不能小於1
basic.param.plan.planNo.max=方案編號不能大於128
basic.param.plan.crossingSeqNo.notNull=路口序號不能為空
basic.param.plan.crossingSeqNo.min=路口序號不能小於1
basic.param.plan.crossingSeqNo.max=路口序號不能大於8
basic.param.plan.phaseTableNo.notNull=相位表不能為空
basic.param.plan.phaseTableNo.min=相位表不能小於1
basic.param.plan.phaseTableNo.max=相位表不能大於10
basic.param.plan.cycle.notNull=週期不能為空
basic.param.plan.cycle.min=週期不能小於0
basic.param.plan.cycle.max=週期不能大於65535
basic.param.plan.coordinatedStageSeq.notNull=協調階段序號不能為空
basic.param.plan.coordinatedStageSeq.min=協調階段序號不能小於0
basic.param.plan.coordinatedStageSeq.max=協調階段序號不能大於16
basic.param.plan.coordinatedRef.notNull=協調參考點不能為空
basic.param.plan.coordinatedRef.min=協調參考點不能小於0
basic.param.plan.coordinatedRef.max=協調參考點不能大於2
basic.param.plan.offset.notNull=偏移量不能為空
basic.param.plan.offset.min=偏移量不能小於0
basic.param.plan.offset.max=偏移量不能大於65535
basic.param.plan.planStageParams.notNull=方案階段參數不能為空
basic.param.plan.planStageParams.size=方案階段參數個數必須在2到16之間
basic.param.plan.planName.notNull=方案名稱不能為空

# 調度參數
basic.param.schedule.scheduleNo.notNull=調度表編號不能為空
basic.param.schedule.scheduleNo.min=調度表編號不能小於1
basic.param.schedule.scheduleNo.max=調度表編號不能大於128
basic.param.schedule.crossingSeqNo.notNull=子路口號不能為空
basic.param.schedule.crossingSeqNo.min=子路口號不能小於1
basic.param.schedule.crossingSeqNo.max=子路口號不能大於8
basic.param.schedule.priority.notNull=優先級不能為空
basic.param.schedule.priority.min=優先級不能小於0
basic.param.schedule.priority.max=優先級不能大於255
basic.param.schedule.week.notNull=星期值不能為空
basic.param.schedule.month.notNull=月份不能為空
basic.param.schedule.day.notNull=日期不能為空
basic.param.schedule.dayPlanNo.notNull=日計劃號不能為空
basic.param.schedule.dayPlanNo.min=日計劃號不能小於1
basic.param.schedule.dayPlanNo.max=日計劃號不能大於128
basic.param.schedule.scheduleName.notNull=調度計劃名稱不能為空

# 階段參數
basic.param.stage.stageParams.notNull=階段相位參數列表不能為空
basic.param.stage.stageParams.size=階段相位參數列表元素應該應在1到64之間
basic.param.stage.startupStages.notNull=子路口啟動參數列表不能為空
basic.param.stage.startupStages.size=子路口啟動參數列表元素應該應在1到8之間

# 階段轉換參數
basic.param.stageTransition.stageTransTables.notNull=階段轉移表不能為空
basic.param.stageTransition.stageTransTables.size=階段轉移表個數應為4
basic.param.stageTransition.modeTrans.notNull=模式階段轉移表不能為空
basic.param.stageTransition.modeTrans.size=模式階段轉移表個數應為255
basic.param.stageTransition.modeTrans.discreteValuesList=模式階段轉移表有效範圍為[1,4]

# 條件計算參數
juncer.param.conditioning.conditioningNo.notNull=條件計算編號不能為空
juncer.param.conditioning.conditioningNo.range=條件計算編號有效範圍為[1,64]
juncer.param.conditioning.outputType.notNull=結果類型不能為空
juncer.param.conditioning.outputNo.notNull=結果編號不能為空
juncer.param.conditioning.op1.notNull=操作符1不能為空
juncer.param.conditioning.paramType1.notNull=變量類型1不能為空
juncer.param.conditioning.paramNo1.notNull=變量編號1不能為空
juncer.param.conditioning.op2.notNull=操作符2不能為空
juncer.param.conditioning.paramType2.notNull=變量類型2不能為空
juncer.param.conditioning.paramNo2.notNull=變量編號2不能為空
juncer.param.conditioning.op3.notNull=操作符3不能為空
juncer.param.conditioning.paramType3.notNull=變量類型3不能為空
juncer.param.conditioning.paramNo3.notNull=變量編號3不能為空
juncer.param.conditioning.op4.notNull=操作符4不能為空
juncer.param.conditioning.paramType4.notNull=變量類型4不能為空
juncer.param.conditioning.paramNo4.notNull=變量編號4不能為空
juncer.param.conditioning.conditioningName.notNull=條件計算名稱不能為空

# 條件動作參數
juncer.param.action.actionNo.notNull=條件動作編號不能為空
juncer.param.action.actionNo.range=條件動作編號有效範圍為[1,64]
juncer.param.action.paramType.notNull=變量類型不能為空
juncer.param.action.paramNo.notNull=變量編號不能為空
juncer.param.action.functionNo.notNull=功能編號不能為空
juncer.param.action.param1.notNull=參數1不能為空
juncer.param.action.param2.notNull=參數2不能為空
juncer.param.action.actionName.notNull=動作名稱不能為空

# Lamp driver board parameters
juncer.param.lampDriverBoard.enabled.range=是否啟用範圍是[0,1]
juncer.param.lampDriverBoard.serialNumber.notNull=燈控板序列號不能為空

# 檢測板參數
juncer.param.detectionBoard.enabled.range=是否啟用範圍是[0,1]
juncer.param.detectionBoard.serialNumber.notNull=檢測板序列號不能為空

# 信息板參數
juncer.param.informationBoard.enabled.range=是否啟用範圍是[0,1]
juncer.param.informationBoard.serialNumber.notNull=信息板序列號不能為空


# LampFaultDetect validation messages
juncer.param.lampFaultDetect.detectCycle.notNull=檢測週期不能為空
juncer.param.lampFaultDetect.detectCycle.range=檢測週期有效範圍是[1,3]
juncer.param.lampFaultDetect.redFaultDetection.notNull=紅燈損壞檢測降級上報配置不能為空
juncer.param.lampFaultDetect.redFaultDetection.range=紅燈損壞檢測降級上報配置有效範圍是[0,4]
juncer.param.lampFaultDetect.greenConflictDetection.notNull=綠衝突檢測降級上報配置不能為空
juncer.param.lampFaultDetect.greenConflictDetection.range=綠衝突檢測降級上報配置有效範圍是[0,4]
juncer.param.lampFaultDetect.redGreenDetection.notNull=紅綠同亮檢測降級上報配置不能為空
juncer.param.lampFaultDetect.redGreenDetection.range=紅綠同亮檢測降級上報配置有效範圍是[0,4]
juncer.param.lampFaultDetect.yellowFaultDetection.notNull=黃燈損壞檢測降級上報配置不能為空
juncer.param.lampFaultDetect.yellowFaultDetection.range=黃燈損壞檢測降級上報配置有效範圍是[0,4]
juncer.param.lampFaultDetect.greenFaultDetection.notNull=綠燈損壞檢測降級上報配置不能為空
juncer.param.lampFaultDetect.greenFaultDetection.range=綠燈損壞檢測降級上報配置有效範圍是[0,4]
juncer.param.lampFaultDetect.redYellowDetection.notNull=紅黃同亮檢測降級上報配置不能為空
juncer.param.lampFaultDetect.redYellowDetection.range=紅黃同亮檢測降級上報配置有效範圍是[0,4]
juncer.param.lampFaultDetect.yellowGreenDetection.notNull=黃綠同亮檢測降級上報配置不能為空
juncer.param.lampFaultDetect.yellowGreenDetection.range=黃綠同亮檢測降級上報配置有效範圍是[0,4]

# Constraint validation messages
juncer.param.constraint.minGreenConstraintLower.notNull=最小綠最小約束不能為空
juncer.param.constraint.minGreenConstraintLower.range=最小綠最小約束有效範圍[0,30]
juncer.param.constraint.minGreenConstraintUpper.notNull=最小綠最大約束不能為空
juncer.param.constraint.minGreenConstraintUpper.range=最小綠最大約束有效範圍[0,30]
juncer.param.constraint.maxGreenConstraintLower.notNull=最大綠最小約束不能為空
juncer.param.constraint.maxGreenConstraintLower.range=最大綠最小約束有效範圍[0,255]
juncer.param.constraint.maxGreenConstraintUpper.notNull=最大綠最大約束不能為空
juncer.param.constraint.maxGreenConstraintUpper.range=最大綠最大約束有效範圍[0,255]
juncer.param.constraint.pedFlashingGreenLower.notNull=行人綠閃最小約束不能為空
juncer.param.constraint.pedFlashingGreenLower.range=行人綠閃最小約束有效範圍[0,32]
juncer.param.constraint.pedFlashingGreenUpper.notNull=行人綠閃最大約束不能為空
juncer.param.constraint.pedFlashingGreenUpper.range=行人綠閃最大約束有效範圍[0,32]
juncer.param.constraint.interGreenLower.notNull=綠間隔最小約束不能為空
juncer.param.constraint.interGreenLower.range=綠間隔最小約束有效範圍[0,30]
juncer.param.constraint.interGreenUpper.notNull=綠間隔最大約束不能為空
juncer.param.constraint.interGreenUpper.range=綠間隔最大約束有效範圍[0,30]
juncer.param.constraint.extGreenLower.notNull=綠延長最小約束不能為空
juncer.param.constraint.extGreenLower.range=綠延長最小約束有效範圍[0,150]
juncer.param.constraint.extGreenUpper.notNull=綠延長最大約束不能為空
juncer.param.constraint.extGreenUpper.range=綠延長最大約束有效範圍[0,150]
# Constraint validation messages
juncer.param.constraint.minGreenConstraint.valid=最小綠最小約束應小於最小綠最大約束
juncer.param.constraint.maxGreenConstraint.valid=最大綠最小約束應小於最大綠最大約束
juncer.param.constraint.pedFlashingGreenConstraint.valid=行人綠閃最小約束應小於行人綠閃最大約束
juncer.param.constraint.interGreenConstraint.valid=綠間隔最小約束應小於綠間隔最大約束
juncer.param.constraint.extGreenConstraint.valid=綠延長最小約束應小於綠延長最大約束


# CountdownInfo validation messages
juncer.param.countdownInfo.enableType.notNull=倒計時啟用類型不能為空
juncer.param.countdownInfo.enableType.range=倒計時啟用類型有效範圍[0,3]
juncer.param.countdownInfo.baudRate.notNull=通信波特率不能為空
juncer.param.countdownInfo.baudRate.range=通信波特率有效範圍[0,3]
juncer.param.countdownInfo.parity.notNull=校驗位不能為空
juncer.param.countdownInfo.parity.range=校驗位有效範圍[0,2]
juncer.param.countdownInfo.pulseBrightWidth.notNull=脈衝亮屏寬度不能為空
juncer.param.countdownInfo.pulseBrightWidth.range=脈衝亮屏寬度有效範圍[0,5]
juncer.param.countdownInfo.pulseBrightColor.notNull=脈衝亮屏燈色不能為空
juncer.param.countdownInfo.pulseBrightColor.range=脈衝亮屏燈色有效範圍[0,1]
juncer.param.countdownInfo.pulseDarkWidth.notNull=脈衝滅屏寬度不能為空
juncer.param.countdownInfo.pulseDarkWidth.range=脈衝滅屏寬度有效範圍[0,5]
juncer.param.countdownInfo.pulseDarkColor.notNull=脈衝滅屏燈色不能為空
juncer.param.countdownInfo.pulseDarkColor.range=脈衝滅屏燈色有效範圍[0,2]
juncer.param.countdownInfo.redVehicleFlag.notNull=觸發紅燈倒計時機動車標誌不能為空
juncer.param.countdownInfo.redVehicleFlag.range=觸發紅燈倒計時機動車標誌有效範圍[0,1]
juncer.param.countdownInfo.redVehicleTime.notNull=觸發紅燈倒計時機動車剩餘時間不能為空
juncer.param.countdownInfo.redVehicleTime.range=觸發紅燈倒計時機動車剩餘時間有效範圍[1,20]
juncer.param.countdownInfo.greenVehicleFlag.notNull=觸發綠燈倒計時機動車標誌不能為空
juncer.param.countdownInfo.greenVehicleFlag.range=觸發綠燈倒計時機動車標誌有效範圍[0,1]
juncer.param.countdownInfo.greenVehicleTime.notNull=觸發綠燈倒計時機動車剩餘時間不能為空
juncer.param.countdownInfo.greenVehicleTime.range=觸發綠燈倒計時機動車剩餘時間有效範圍[1,20]
juncer.param.countdownInfo.redPedestrianFlag.notNull=觸發紅燈倒計時行人標誌不能為空
juncer.param.countdownInfo.redPedestrianFlag.range=觸發紅燈倒計時行人標誌有效範圍[0,1]
juncer.param.countdownInfo.redPedestrianTime.notNull=觸發紅燈倒計時行人剩餘時間不能為空
juncer.param.countdownInfo.redPedestrianTime.range=觸發紅燈倒計時行人剩餘時間有效範圍[1,20]
juncer.param.countdownInfo.greenPedestrianFlag.notNull=觸發綠燈倒計時行人標誌不能為空
juncer.param.countdownInfo.greenPedestrianFlag.range=觸發綠燈倒計時行人標誌有效範圍[0,1]
juncer.param.countdownInfo.greenPedestrianTime.notNull=觸發綠燈倒計時行人剩餘時間不能為空
juncer.param.countdownInfo.greenPedestrianTime.range=觸發綠燈倒計時行人剩餘時間有效範圍[1,20]
juncer.param.countdownInfo.inductionFlag.notNull=感應模式觸發倒計時標誌不能為空
juncer.param.countdownInfo.inductionFlag.range=感應模式觸發倒計時標誌有效範圍[0,1]

# CountdownDisplayParam validation messages
juncer.param.countdownDisplayParam.countdownDisplayNo.notNull=倒計時屏編號不能為空
juncer.param.countdownDisplayParam.countdownDisplayNo.range=倒計時屏編號有效範圍[1,32]
juncer.param.countdownDisplayParam.phase1.notNull=相位1不能為空
juncer.param.countdownDisplayParam.phase1.range=相位號1有效範圍[0,64]
juncer.param.countdownDisplayParam.phase2.notNull=相位2不能為空
juncer.param.countdownDisplayParam.phase2.range=相位號2有效範圍[0,64]

# SegmentAction validation messages
juncer.param.segmentAction.functionNo.notNull=功能編號不能為空
juncer.param.segmentAction.param1.notNull=參數1不能為空
juncer.param.segmentAction.param2.notNull=參數2不能為空

# SegmentParam validation messages
juncer.param.segmentParam.startTime.notNull=開始時間不能為空
juncer.param.segmentParam.startTime.range=開始時間有效範圍為[0,1439]
juncer.param.segmentParam.planNo.notNull=方案號不能為空
juncer.param.segmentParam.planNo.range=方案號有效範圍為[1,128]
juncer.param.segmentParam.controlMode.notNull=控制方式不能為空
juncer.param.segmentParam.controlMode.range=控制方式有效範圍為[0,255]
juncer.param.segmentParam.coordCorrectionMode.notNull=方案過渡方式不能為空
juncer.param.segmentParam.coordCorrectionMode.range=方案過渡方式有效範圍為[0,3]
juncer.param.segmentParam.coordDirection.notNull=協調方向不能為空
juncer.param.segmentParam.coordDirection.range=協調方向有效範圍為[0,255]
juncer.param.segmentParam.actionNum.notNull=動作鏈個數不能為空
juncer.param.segmentParam.actionNum.range=動作鏈個數有效範圍為[0,16]
juncer.param.segmentParam.segmentActions.notNull=動作鏈不能為空
juncer.param.segmentParam.segmentActions.size=動作鏈數量有效範圍為[0,16]

# Emergency validation messages
juncer.param.emergency.emergencyNo.notNull=緊急編號不能為空
juncer.param.emergency.emergencyNo.range=緊急編號有效範圍為[1,64]
juncer.param.emergency.stageNo.notNull=緊急申請相位階段不能為空
juncer.param.emergency.stageNo.range=緊急申請相位階段有效範圍為[1,64]
juncer.param.emergency.priority.notNull=緊急申請優先級不能為空
juncer.param.emergency.priority.range=緊急申請優先級有效範圍為[0,255]
juncer.param.emergency.delayTime.notNull=延遲時間不能為空
juncer.param.emergency.delayTime.range=延遲時間有效範圍為[0,255]
juncer.param.emergency.durationTime.notNull=持續時間不能為空
juncer.param.emergency.durationTime.range=持續時間有效範圍為[0,255]
juncer.param.emergency.callIntervalTime.notNull=呼叫間隔時間不能為空
juncer.param.emergency.callIntervalTime.range=呼叫間隔時間有效範圍為[0,255]
juncer.param.emergency.paramTypeCall.notNull=需求變量類型不能為空
juncer.param.emergency.paramNoCall.notNull=需求變量編號不能為空
juncer.param.emergency.paramTypeCancel.notNull=刪除變量類型不能為空
juncer.param.emergency.paramNoCancel.notNull=刪除變量編號不能為空
juncer.param.emergency.paramTypeConfirm.notNull=確認變量類型不能為空
juncer.param.emergency.paramNoConfirm.notNull=確認變量編號不能為空
juncer.param.emergency.callWatchDog.notNull=緊急優先呼叫看門狗不能為空
juncer.param.emergency.callWatchDog.range=緊急優先呼叫看門狗有效範圍為[0,255]

# ManualPanel validation messages
juncer.param.manualPanel.buttonNo.notNull=手控按鈕編號不能為空
juncer.param.manualPanel.buttonNo.range=手控按鈕編號有效範圍為[1,8]
juncer.param.manualPanel.callStageNo.notNull=呼叫階段編號不能為空
juncer.param.manualPanel.callStageNo.size=呼叫階段編號數量必須為8
juncer.param.manualPanel.callStageNo.range=呼叫階段編號有效範圍為[0,64]

# IncompatiblePhase validation messages
juncer.param.incompatiblePhase.phaseNo.notNull=相位編號不能為空
juncer.param.incompatiblePhase.phaseNo.range=相位編號有效範圍是[1,64]
juncer.param.incompatiblePhase.incompatiblePhaseSeq.notNull=衝突對立相位序列不能為空
juncer.param.incompatiblePhase.incompatiblePhaseSeq.size=衝突對立相位序列長度必須為[1,64]
juncer.param.incompatiblePhase.incompatiblePhaseSeq.range=衝突對立相位序列有效範圍是[0,3]

# IncompatiblePhases validation messages
juncer.param.incompatiblePhases.incompatiblePhases.notNull=相位衝突配置表列表不能為空
juncer.param.incompatiblePhases.incompatiblePhases.size=相位衝突配置表列表長度必須為[1,64]

# InterGreenTime validation messages
juncer.param.interGreenTime.phaseNo.notNull=相位編號不能為空
juncer.param.interGreenTime.phaseNo.range=相位編號有效範圍是[1,64]
juncer.param.interGreenTime.interGreenTimeSeq.notNull=綠間隔時間序列不能為空
juncer.param.interGreenTime.interGreenTimeSeq.size=綠間隔時間序列長度必須為64
juncer.param.interGreenTime.interGreenTimeSeq.range=綠間隔時間序列有效範圍是[0,30]

# InterGreenTimeTable validation messages
juncer.param.interGreenTimeTable.interGreenTimeTableNo.notNull=相位綠間隔表號不能為空
juncer.param.interGreenTimeTable.interGreenTimeTableNo.range=相位綠間隔表號有效範圍是[1,4]
juncer.param.interGreenTimeTable.interGreenTimes.notNull=相位綠間隔配置列表不能為空
juncer.param.interGreenTimeTable.interGreenTimes.size=相位綠間隔配置列表長度必須為64

# UIThreshold validation messages
juncer.param.uiThreshold.voltageThresholdUpper.notNull=電壓閾值上限不能為空
juncer.param.uiThreshold.voltageThresholdUpper.range=電壓閾值上限有效範圍是[0,255]
juncer.param.uiThreshold.voltageThresholdLower.notNull=電壓閾值下限不能為空
juncer.param.uiThreshold.voltageThresholdLower.range=電壓閾值下限有效範圍是[0,255]
juncer.param.uiThreshold.currentThresholdUpper.notNull=電流閾值上限不能為空
juncer.param.uiThreshold.currentThresholdUpper.range=電流閾值上限有效範圍是[0,255]
juncer.param.uiThreshold.currentThresholdLower.notNull=電流閾值下限不能為空
juncer.param.uiThreshold.currentThresholdLower.range=電流閾值下限有效範圍是[0,255]
juncer.param.uiThreshold.voltageThreshold.valid=電壓閾值上限必須大於電壓閾值下限
juncer.param.uiThreshold.currentThreshold.valid=電流閾值上限必須大於電流閾值下限

# Threshold validation messages
juncer.param.threshold.voltageUpper.notNull=電壓閾值上限不能為空
juncer.param.threshold.voltageLower.notNull=電壓閾值下限不能為空
juncer.param.threshold.currentUpper.notNull=電流閾值上限不能為空
juncer.param.threshold.currentLower.notNull=電流閾值下限不能為空
juncer.param.threshold.voltageUpper.range=電壓閾值上限有效範圍是[0,510]
juncer.param.threshold.voltageLower.range=電壓閾值下限有效範圍是[0,510]
juncer.param.threshold.currentUpper.range=電流閾值上限有效範圍是[0,3000]
juncer.param.threshold.currentLower.range=電流閾值下限有效範圍是[0,3000]
juncer.param.threshold.voltage.upperGreaterThanLower=電壓上限必須大於電壓下限
juncer.param.threshold.current.upperGreaterThanLower=電流上限必須大於電流下限

# LightTransition validation messages
juncer.param.lightTransition.colorType.notNull=燈色類型不能為空
juncer.param.lightTransition.colorType.range=燈色類型有效範圍為[0,14]
juncer.param.lightTransition.colorTime.notNull=燈色時長不能為空
juncer.param.lightTransition.colorTime.range=燈色時長有效範圍為[0,99]

# UserFlag validation messages
juncer.param.userFlag.userFlagNo.notNull=用戶變量編號不能為空
juncer.param.userFlag.userFlagNo.range=用戶變量編號有效範圍為[1,64]
juncer.param.userFlag.userFlagName.notNull=用戶變量名稱不能為空

# Timer validation messages
juncer.param.timer.timerNo.notNull=定時器編號不能為空
juncer.param.timer.timerNo.range=定時器編號有效範圍為[1,64]
juncer.param.timer.timerName.notNull=定時器名稱不能為空
juncer.param.timer.interval.notNull=時間間隔不能為空
juncer.param.timer.interval.range=時間間隔有效範圍為[0,65535]

# LogicOutput validation messages
juncer.param.logicOutput.logicOutputNo.notNull=邏輯輸出編號不能為空
juncer.param.logicOutput.logicOutputNo.range=邏輯輸出編號有效範圍為[1,64]
juncer.param.logicOutput.logicOutputName.notNull=邏輯輸出名稱不能為空

# Demand validation messages
juncer.param.demand.inputType.notNull=需求類型不能為空
juncer.param.demand.inputNo.notNull=需求編號不能為空
juncer.param.demand.inputFlag.notNull=參數標記不能為空

# Extension validation messages
juncer.param.extension.inputType.notNull=需求類型不能為空
juncer.param.extension.inputNo.notNull=需求編號不能為空
juncer.param.extension.inputFlag.notNull=參數標記不能為空

# StageTransitionConstraint validation messages
juncer.param.stageTransitionConstraint.stageNo.notNull=相位階段編號不能為空
juncer.param.stageTransitionConstraint.stageNo.range=相位階段編號有效範圍是[1,64]
juncer.param.stageTransitionConstraint.transitionConstraints.notNull=相位階段過渡約束值不能為空
juncer.param.stageTransitionConstraint.transitionConstraints.size=相位階段過渡約束值長度必須為64
juncer.param.stageTransitionConstraint.transitionConstraints.range=相位階段過渡約束值有效範圍是[0,64]及255

# StageParamInfo validation messages
basic.param.stageParam.demandsInsertLeavingHurryCall.notNull=緊急調用結束後插入需求不能為空
basic.param.stageParam.demandsInsertLeavingHurryCall.range=緊急調用結束後插入需求有效範圍為[0,1]
basic.param.stageParam.demandsInsertLeavingSystem.notNull=中心控制結束後插入需求不能為空
basic.param.stageParam.demandsInsertLeavingSystem.range=中心控制結束後插入需求有效範圍為[0,1]
basic.param.stageParam.unconditionalDemand.notNull=無條件默認需求不能為空
basic.param.stageParam.unconditionalDemand.range=無條件默認需求有效範圍為[0,1]



