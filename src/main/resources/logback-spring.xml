<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!--
    Logback 配置文件详细说明：
    - scan="true": 启用配置文件自动扫描，当配置文件发生变化时自动重新加载
    - scanPeriod="30 seconds": 扫描间隔为30秒，即每30秒检查一次配置文件是否有变化
-->
<configuration scan="true" scanPeriod="30 seconds">

    <!--
        引入 Spring Boot 默认配置
        - 包含了 Spring Boot 提供的默认日志配置，如颜色输出、默认格式等
        - 提供了一些预定义的变量和配置，如 PID、CONSOLE_LOG_PATTERN 等
    -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!--
        获取应用名称
        - scope="context": 变量作用域为上下文级别，整个配置文件中都可以使用
        - name="application_name": 定义变量名
        - source="spring.application.name": 从 Spring 配置中获取应用名称
        - defaultValue="openles": 如果获取不到，则使用默认值 "openles"
    -->
    <springProperty scope="context" name="application_name" source="spring.application.name" defaultValue="openles"/>

    <!--
        日志存储路径
        - 从 Spring 配置的 logging.file.path 属性中获取日志文件存储路径
        - 如果未配置，则默认使用 "daa-log" 目录
    -->
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path" defaultValue="daa-log"/>

    <!--
        日志格式定义
        各个占位符的含义：
        - %d{yyyy-MM-dd HH:mm:ss.SSS}: 时间戳，格式为年-月-日 时:分:秒.毫秒
        - [%-18thread]: 线程名，左对齐，最小宽度18字符
        - %-5level: 日志级别，左对齐，最小宽度5字符
        - %logger{36}: Logger名称，最大长度36字符，超出部分会被缩写
        - %msg: 日志消息内容
        - %n: 换行符
        - %exception{full}: 完整的异常堆栈信息
    -->
    <property name="PATTERN_FORMAT" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-18thread] %-5level %logger{36} - %msg%n"/>
    <!-- 带异常信息的日志格式，用于错误日志 -->
    <property name="PATTERN_FORMAT_WITH_EX" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-18thread] %-5level %logger{36} - %msg%n%exception{full}"/>
    <!-- 简化的日志格式，用于特定业务日志（如设备通信日志） -->
    <property name="PATTERN_FORMAT_JUNCER" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-18.18thread] - %msg%n"/>

    <!--
        控制台输出 Appender
        - ConsoleAppender: 将日志输出到控制台（标准输出）
        - 使用彩色输出格式，便于开发调试
    -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--
                控制台日志格式，包含颜色高亮：
                - %highlight(%-5level): 日志级别高亮显示
                - %magenta(${PID:- }): 进程ID，洋红色显示
                - %yellow(%-18thread): 线程名，黄色显示
                - %cyan(%logger{36}): Logger名称，青色显示
            -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%highlight(%-5level)] [%magenta(${PID:- })] [%yellow(%-18thread)] %cyan(%logger{36}) - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--
        系统日志文件 Appender
        - RollingFileAppender: 滚动文件输出器，支持文件大小和时间的滚动策略
        - 用于记录应用的所有日志信息
    -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志文件路径，使用变量替换 -->
        <file>${LOG_PATH}/openles/${application_name:-openles}.log</file>

        <!--
            滚动策略配置
            - SizeAndTimeBasedRollingPolicy: 基于文件大小和时间的滚动策略
        -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--
                滚动文件命名模式：
                - %d{yyyy-MM-dd}: 按日期滚动
                - %i: 同一天内的文件序号（当文件大小超过限制时）
                - .gz: 压缩存储，节省磁盘空间
            -->
            <fileNamePattern>${LOG_PATH}/openles/${application_name:-openles}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 单个日志文件最大大小，超过后会创建新文件 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保留历史日志文件的天数，超过30天的文件会被删除 -->
            <maxHistory>30</maxHistory>
            <!-- 所有日志文件的总大小上限，超过2GB后会删除最旧的文件 -->
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <!-- 使用预定义的标准日志格式 -->
            <pattern>${PATTERN_FORMAT}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--
        错误日志文件 Appender
        - 专门用于记录 ERROR 级别及以上的日志
        - 便于快速定位和分析系统错误
    -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 错误日志文件路径，文件名包含 "-error" 标识 -->
        <file>${LOG_PATH}/openles/${application_name:-openles}-error.log</file>

        <!-- 与系统日志相同的滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/openles/${application_name:-openles}-error-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <!-- 使用包含完整异常信息的日志格式 -->
            <pattern>${PATTERN_FORMAT_WITH_EX}</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!--
            阈值过滤器
            - ThresholdFilter: 只允许指定级别及以上的日志通过
            - level="ERROR": 只记录 ERROR 和 FATAL 级别的日志
        -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!--
        基于 IP 地址的日志文件 Appender
        - SiftingAppender: 筛选输出器，可以根据不同的条件创建不同的子输出器
        - 用于按客户端IP地址分别记录设备通信日志，便于问题定位和分析
    -->
    <appender name="IP_BASED_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <!--
            判别器配置
            - 根据 clientIP 变量的值来区分不同的日志文件
            - 每个不同的 IP 地址会创建独立的日志文件
        -->
        <discriminator>
            <key>clientIP</key>
            <defaultValue>unknown</defaultValue>
        </discriminator>

        <!--
            自定义过滤器
            - 使用项目中自定义的 IP 基础日志过滤器
            - 只有通过过滤器的日志才会被记录到对应的IP日志文件中
        -->
        <filter class="com.les.its.open.area.net.log.IpBasedLoggingFilter">
            <clientIP>${clientIP}</clientIP>
        </filter>

        <!--
            筛选配置
            - 为每个不同的 clientIP 创建独立的 RollingFileAppender
        -->
        <sift>
            <appender name="FILE-${clientIP}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <!-- 按IP地址创建不同目录的设备日志文件 -->
                <file>${LOG_PATH}/juncer/${clientIP}/device.log</file>

                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>${LOG_PATH}/juncer/${clientIP}/device-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                    <maxFileSize>100MB</maxFileSize>
                    <maxHistory>30</maxHistory>
                    <!-- 每个IP的日志总大小限制为5GB，比系统日志更大，因为设备通信日志可能较多 -->
                    <totalSizeCap>5GB</totalSizeCap>
                </rollingPolicy>

                <encoder>
                    <!-- 使用简化的日志格式，专门用于设备通信日志 -->
                    <pattern>${PATTERN_FORMAT_JUNCER}</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>


    <!--
        基于信号机ID的日志文件 Appender
        - 类似于IP基础日志，但是按信号机ID进行分类
        - 用于记录不同信号机的控制消息日志
    -->
    <appender name="SIGNAL_BASED_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <!--
            判别器配置
            - 根据 signalId 变量的值来区分不同的日志文件
            - 每个不同的信号机ID会创建独立的日志文件
        -->
        <discriminator>
            <key>signalId</key>
            <defaultValue>unknown</defaultValue>
        </discriminator>

        <!--
            自定义过滤器
            - 使用项目中自定义的信号机基础日志过滤器
            - 只有通过过滤器的日志才会被记录到对应的信号机日志文件中
        -->
        <filter class="com.les.its.open.area.message.log.SignalBasedLoggingFilter">
            <signalId>${signalId}</signalId>
        </filter>

        <!--
            筛选配置
            - 为每个不同的 signalId 创建独立的 RollingFileAppender
        -->
        <sift>
            <appender name="FILE-${signalId}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <!-- 按信号机ID创建不同目录的控制日志文件 -->
                <file>${LOG_PATH}/message/${signalId}/control.log</file>

                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>${LOG_PATH}/message/${signalId}/control-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                    <maxFileSize>100MB</maxFileSize>
                    <maxHistory>30</maxHistory>
                    <!-- 每个信号机的日志总大小限制为5GB -->
                    <totalSizeCap>5GB</totalSizeCap>
                </rollingPolicy>

                <encoder>
                    <!-- 使用简化的日志格式，专门用于信号机控制日志 -->
                    <pattern>${PATTERN_FORMAT_JUNCER}</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <!--
        异步日志处理 Appender
        - AsyncAppender: 异步输出器，将日志写入操作放到后台线程执行
        - 提高应用性能，避免日志写入阻塞主业务线程
    -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!--
            丢弃阈值设置为0
            - 当队列使用率达到阈值时，会丢弃TRACE、DEBUG、INFO级别的日志
            - 设置为0表示不丢弃任何日志，确保日志完整性
        -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 异步队列大小，缓存512条日志消息 -->
        <queueSize>512</queueSize>
        <!-- 引用系统日志文件 Appender -->
        <appender-ref ref="FILE"/>
    </appender>

    <!--
        异步错误日志处理 Appender
        - 专门用于异步处理错误日志
    -->
    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <!-- 引用错误日志文件 Appender -->
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!--
        第三方框架日志级别控制
        - 通过设置不同框架的日志级别，减少不必要的日志输出
        - 提高系统性能，避免日志文件过大
    -->
    <!-- Spring 框架日志级别设置为 ERROR，只记录错误信息 -->
    <logger name="org.springframework" level="ERROR"/>
    <!-- Apache 相关组件日志级别 -->
    <logger name="org.apache" level="ERROR"/>
    <!-- Hibernate ORM 框架日志级别 -->
    <logger name="org.hibernate" level="ERROR"/>
    <!-- MongoDB 驱动日志级别设置为 ERROR，只记录错误 -->
    <logger name="org.mongodb" level="ERROR"/>
    <!-- Kafka 客户端日志级别 -->
    <logger name="org.apache.kafka" level="ERROR"/>
    <!-- Apache HTTP 客户端日志级别 -->
    <logger name="org.apache.http" level="ERROR"/>
    <!-- HikariCP 连接池日志级别 -->
    <logger name="com.zaxxer" level="ERROR"/>
    <!-- Netty 网络框架日志级别 -->
    <logger name="io.netty" level="ERROR"/>
    <!-- SLF4J 日志框架本身的日志级别 -->
    <logger name="org.slf4j" level="ERROR"/>
    <!-- Logback 本身的日志级别设置为 ERROR，只记录错误信息 -->
    <logger name="ch.qos.logback" level="ERROR"/>

    <!--
        网络通信日志配置
        - 专门用于记录设备网络通信相关的日志
        - level="ERROR": 只记录错误级别的日志
        - additivity="false": 不向父Logger传播，避免重复记录
    -->
    <logger name="com.les.its.open.area.net.log" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="IP_BASED_FILE"/>
    </logger>

    <!--
        消息处理日志配置
        - 专门用于记录信号机消息处理相关的日志
        - level="ERROR": 只记录错误级别的日志
        - additivity="false": 不向父Logger传播
    -->
    <logger name="com.les.its.open.area.message.log" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="SIGNAL_BASED_FILE"/>
    </logger>

    <!--
        应用日志级别控制
        - 针对项目主包 com.les.its.open 的日志配置
        - level="ERROR": 只记录错误级别的日志
        - additivity="false": 不向根Logger传播，避免重复记录
    -->
    <logger name="com.les.its.open" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </logger>

    <!--
        根日志级别配置
        - 所有未明确配置Logger的类都会使用根Logger的配置
        - level="ERROR": 根级别设置为ERROR，只记录错误级别的日志
        - 包含了所有主要的Appender，确保日志的完整记录
    -->
    <root level="ERROR">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="ASYNC_ERROR"/>
        <appender-ref ref="IP_BASED_FILE"/>
        <appender-ref ref="SIGNAL_BASED_FILE"/>
    </root>
</configuration>
