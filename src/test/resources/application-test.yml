server:
  port: 0  # 随机端口用于测试

spring:
  application:
    name: openles-test
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    database: h2
    show-sql: false
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        show_sql: false
        format_sql: false
        cache:
          use_second_level_cache: false
  h2:
    console:
      enabled: true
      path: /h2-test
      settings:
        web-allow-others: false
  data:
    redis:
      # 使用embedded Redis for testing
      host: localhost
      port: 6370  # 不同端口避免冲突
      database: 15
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  rabbitmq:
    # 测试环境使用模拟MQ
    host: localhost
    port: 5673  # 测试端口
    username: test
    password: test
    virtual-host: /test

logging:
  level:
    com.les.its.open: DEBUG
    org.springframework.test: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 测试专用配置
global:
  areaNo: 99  # 测试区域号
  cityCode: 999999
  enableUseSgp: false
  enableCheckSignalAndCrossingData: false
  brandList: 
  areaList: 99
  maxSystemControlTime: 300  # 测试环境缩短时间

its:
  server:
    local:
      listens:
        port: 17301  # 测试端口
        protocol: OPENLES
      connect:
        ip: 127.0.0.1
        port: 17301
        localPort: 17302
        protocol: OPENLES

test:
  useTest: true
  signal-map:
    signal:
      signalId: TEST_SIGNAL_001
      crossingIds: TEST_CROSSING_001
      noArea: 99
      noJunc: 1
      subJuncNos: 1
      ip: 127.0.0.1
      port: 17000
    signal2:
      signalId: TEST_SIGNAL_002
      crossingIds: TEST_CROSSING_002
      noArea: 99
      noJunc: 2
      subJuncNos: 1
      ip: 127.0.0.1
      port: 17001

app:
  locale:
    default-locale: zh_CN
  token:
    check: false  # 测试环境关闭token验证