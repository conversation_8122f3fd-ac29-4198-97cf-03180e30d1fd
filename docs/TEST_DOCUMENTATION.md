# OpenLES 测试文档

## 概述

本文档详细描述了OpenLES交通信号控制系统的完整测试架构、测试策略和测试实现。测试覆盖了从单元测试到集成测试的全方位测试场景，确保系统的可靠性、性能和稳定性。

## 测试架构

### 测试层次结构

```
src/test/java/
├── com/les/its/open/
│   ├── common/                          # 测试基础设施
│   │   ├── BaseTest.java               # Spring Boot集成测试基类
│   │   ├── BaseUnitTest.java           # 单元测试基类
│   │   ├── TestDataFactory.java        # 测试数据工厂
│   │   ├── NetworkTestUtils.java       # 网络测试工具
│   │   └── DistributedTestUtils.java   # 分布式系统测试工具
│   ├── area/                           # 业务层测试
│   │   ├── juncer/api/                # API控制器测试
│   │   ├── message/handler/           # 消息处理器测试
│   │   └── message/service/           # 业务服务测试
│   ├── protocol/                       # 协议层测试
│   │   └── openles/codec/             # 协议编解码测试
│   ├── netty/                          # 网络通信测试
│   └── integration/                    # 集成测试
│       ├── OpenLesSystemIntegrationTest.java
│       ├── DatabaseIntegrationTest.java
│       ├── MessageQueueIntegrationTest.java
│       ├── RedisIntegrationTest.java
│       └── IntegrationTestSuite.java
```

### 测试配置

- **测试环境**: 使用H2内存数据库和嵌入式消息队列
- **测试配置文件**: `src/test/resources/application-test.yml`
- **测试数据**: 通过TestDataFactory统一管理
- **Mock框架**: Mockito 4.x
- **断言库**: AssertJ
- **测试运行器**: JUnit 5

## 测试分类

### 1. 单元测试 (Unit Tests)

#### API控制器测试
- **CmdControllerEnhancedTest**: 命令控制器完整测试
- **AllLookSignalControllerTest**: 批量参数查询控制器测试
- **LoadSignalControllerTest**: 参数加载控制器测试
- **LookSignalControllerTest**: 单参数查询控制器测试

**测试覆盖**:
- 所有REST端点的正常场景
- 异常处理和边界条件
- 请求参数验证
- 响应格式验证
- 并发访问测试

#### 消息处理器测试
- **TabSetStageHandlerTest**: 阶段设置消息处理器测试
- **基础消息处理器测试**: 涵盖17+种消息处理器

**测试覆盖**:
- 消息解析和验证
- 业务逻辑处理
- 错误处理机制
- 消息应答生成

#### 协议编解码测试
- **OpenLesMessageEncoderTest**: 消息编码器测试
- **OpenLesMessageDecoderTest**: 消息解码器测试

**测试覆盖**:
- 二进制消息编码/解码
- 消息格式验证
- 错误消息处理
- 性能基准测试

#### 网络通信测试
- **NettyServerTest**: Netty服务器测试

**测试覆盖**:
- 服务器启动/关闭
- 客户端连接管理
- 消息传输
- 连接异常处理

#### 业务服务测试
- **ControllerServiceTest**: 控制器服务测试
- **EnhancedDistributedLockServiceTest**: 分布式锁服务测试
- **DistributedRateLimitServiceTest**: 分布式限流服务测试

**测试覆盖**:
- 业务逻辑验证
- 并发安全性
- 性能测试
- 异常处理

### 2. 集成测试 (Integration Tests)

#### 系统集成测试 (OpenLesSystemIntegrationTest)
- **端到端协议通信测试**: 完整的信号控制器注册和通信流程
- **命令执行流程测试**: SetStage命令的完整执行链路
- **参数查询流程测试**: 参数查询的端到端测试
- **多客户端并发测试**: 多个信号控制器同时连接的场景
- **错误恢复测试**: 网络异常和服务重启的恢复机制

#### 数据库集成测试 (DatabaseIntegrationTest)
- **基础连接测试**: 数据库连接池和基本操作
- **实体持久化测试**: JPA实体的CRUD操作
- **事务管理测试**: 事务回滚和一致性保证
- **并发访问测试**: 多线程数据库操作
- **性能测试**: 批量操作和查询优化

#### 消息队列集成测试 (MessageQueueIntegrationTest)
- **基础消息操作**: RabbitMQ消息发送和接收
- **消息路由测试**: Exchange和Binding的路由机制
- **消息确认测试**: 手动ACK和NACK处理
- **OpenLES消息处理**: 特定业务消息的处理流程
- **高并发消息测试**: 大量消息的并发处理

#### Redis集成测试 (RedisIntegrationTest)
- **基础Redis操作**: String、Hash、Set等数据结构操作
- **分布式锁测试**: Redisson分布式锁的功能验证
- **限流服务测试**: Bucket4j分布式限流功能
- **缓存服务测试**: 信号机数据缓存机制
- **连接池测试**: Redis连接池的并发处理能力

## 测试工具和基础设施

### TestDataFactory
```java
public class TestDataFactory {
    // 统一的测试数据常量
    public static final String TEST_SIGNAL_ID = "TEST_SIGNAL_001";
    public static final String TEST_CROSSING_ID = "CROSSING_001";
    public static final String TEST_IP = "*************";
    public static final int TEST_PORT = 7301;
    
    // 测试对象创建方法
    public static ControllerAgent createTestControllerAgent() { ... }
    public static TabSetStage createTestTabSetStage() { ... }
    public static Phase createTestPhase() { ... }
    // ... 更多工厂方法
}
```

### NetworkTestUtils
```java
public class NetworkTestUtils {
    // 网络测试工具方法
    public static int findAvailablePort() { ... }
    public static ByteBuf createTestByteBuf(String data) { ... }
    public static void waitForCondition(Supplier<Boolean> condition, long timeoutMs, long intervalMs) { ... }
}
```

### DistributedTestUtils
```java
public class DistributedTestUtils {
    // 分布式系统测试工具
    public static void setupDistributedLock(String lockKey) { ... }
    public static void verifyRateLimit(String key, int expectedLimit) { ... }
}
```

## 测试策略

### 1. 测试金字塔策略
- **单元测试 (70%)**: 快速执行，覆盖核心业务逻辑
- **集成测试 (20%)**: 验证组件间交互
- **端到端测试 (10%)**: 验证完整用户场景

### 2. 测试隔离策略
- **数据隔离**: 每个测试使用独立的测试数据
- **资源隔离**: 测试间不共享状态和资源
- **环境隔离**: 使用内存数据库和嵌入式服务

### 3. 测试自动化策略
- **持续集成**: Maven测试生命周期集成
- **并行执行**: 支持多线程测试执行
- **失败快速**: 一旦发现问题立即停止

### 4. 测试覆盖策略
- **代码覆盖率**: 目标80%+的行覆盖率
- **分支覆盖率**: 目标70%+的分支覆盖率
- **功能覆盖**: 100%核心功能测试覆盖

## 测试执行

### 运行所有测试
```bash
mvn clean test
```

### 运行特定测试类
```bash
mvn test -Dtest=CmdControllerEnhancedTest
```

### 运行集成测试
```bash
mvn test -Dtest=IntegrationTestSuite
```

### 运行测试并生成报告
```bash
mvn test jacoco:report
```

## 测试报告

### 测试覆盖率报告
- 位置: `target/site/jacoco/index.html`
- 包含: 行覆盖率、分支覆盖率、复杂度分析

### 测试执行报告
- 位置: `target/surefire-reports/`
- 格式: XML和TXT格式的测试结果

### 集成测试报告
- 自动生成测试摘要和性能指标
- 包含失败测试的详细信息
- 提供系统健康状态检查

## 测试最佳实践

### 1. 命名规范
- 测试类: `XxxTest` 或 `XxxIntegrationTest`
- 测试方法: `should[预期结果]When[条件]` 格式
- 显示名称: 使用中文描述测试意图

### 2. 测试结构
```java
@Test
@DisplayName("应该在有效输入时返回成功结果")
void shouldReturnSuccessWhenValidInput() {
    // Arrange - 准备测试数据和环境
    
    // Act - 执行被测试的操作
    
    // Assert - 验证结果
}
```

### 3. Mock使用原则
- 只对外部依赖进行Mock
- 避免过度Mock导致测试脆弱
- 使用@InjectMocks注解注入依赖

### 4. 断言策略
- 使用AssertJ提供的流式断言
- 提供清晰的断言失败消息
- 验证关键业务指标

### 5. 测试数据管理
- 使用TestDataFactory统一创建测试数据
- 避免测试间的数据依赖
- 清理测试产生的临时数据

## 性能测试

### 1. 单元测试性能要求
- 每个测试方法执行时间 < 1秒
- 整个测试类执行时间 < 30秒

### 2. 集成测试性能要求
- 数据库操作测试 < 5秒
- 网络通信测试 < 10秒
- 完整集成测试 < 60秒

### 3. 性能监控
- 记录关键操作的执行时间
- 监控资源使用情况
- 设置性能回归检测

## 错误处理测试

### 1. 异常场景覆盖
- 网络连接异常
- 数据库连接失败
- 消息解析错误
- 业务逻辑异常

### 2. 边界条件测试
- 空值和null处理
- 超长字符串输入
- 数值边界测试
- 并发极限测试

### 3. 恢复机制测试
- 自动重连测试
- 事务回滚测试
- 缓存清理测试
- 资源释放测试

## 持续改进

### 1. 测试质量度量
- 定期评估测试覆盖率
- 分析测试失败趋势
- 监控测试执行时间

### 2. 测试维护
- 及时更新过时的测试
- 重构重复的测试代码
- 优化慢速测试

### 3. 团队协作
- 代码审查包含测试代码
- 共享测试最佳实践
- 定期进行测试培训

## 故障排除

### 常见问题及解决方案

#### 1. 端口冲突
```
问题: java.net.BindException: Address already in use
解决: 使用NetworkTestUtils.findAvailablePort()动态分配端口
```

#### 2. 数据库连接超时
```
问题: Connection timeout
解决: 检查H2数据库配置，确保连接池设置正确
```

#### 3. Mock对象未正确注入
```
问题: NullPointerException in @InjectMocks
解决: 检查@Mock注解和MockitoExtension配置
```

#### 4. 并发测试不稳定
```
问题: 并发测试偶尔失败
解决: 使用CountDownLatch和适当的等待时间
```

## 测试环境配置

### application-test.yml
```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      
logging:
  level:
    com.les.its.open: DEBUG
    org.springframework.test: INFO
```

## 总结

OpenLES测试框架提供了全面的测试覆盖，从单元测试到集成测试，确保系统的各个层次都得到充分验证。通过采用现代化的测试工具和最佳实践，我们构建了一个可靠、高效、易维护的测试体系。

**测试统计**:
- 总测试类数: 15+
- 总测试方法数: 200+
- 预期代码覆盖率: 80%+
- 平均测试执行时间: < 2分钟

这个测试框架不仅保证了当前功能的正确性，也为未来的功能扩展和重构提供了可靠的安全网。